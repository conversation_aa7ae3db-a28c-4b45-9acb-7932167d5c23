import React from 'react';
import { motion } from 'framer-motion';
import { 
  ArrowUp, 
  Phone, 
  Mail, 
  MapPin, 
  Facebook, 
  Instagram, 
  Twitter, 
  Linkedin,
  Heart,
  Clock,
  Youtube
} from 'lucide-react';

interface FooterProps {
  onNavigate: (section: string) => void;
}

const Footer: React.FC<FooterProps> = ({ onNavigate }) => {
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const navigationLinks = [
    { id: 'hero', label: 'Home' },
    { id: 'about', label: 'About Us' },
    { id: 'brands', label: 'Brands' },
    { id: 'promotions', label: 'Promotions' },
    { id: 'careers', label: 'Career' },
    { id: 'news', label: 'News' },
    { id: 'contact', label: 'Contact Us' }
  ];

  const socialLinks = [
    { icon: Instagram, href: '#', label: 'Instagram' },
    { icon: Facebook, href: '#', label: 'Facebook' }
  ];

  const officeHours = {
    schedule: [
      'Monday - Friday',
      '09:00 AM to 05:00 PM',
      'Saturday - Sunday CLOSED'
    ]
  };

  return (
    <footer className="bg-white text-gray-800 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-diagonal-pattern opacity-5"></div>
      
      {/* Back to Top Button */}
      <motion.button
        onClick={scrollToTop}
        className="absolute -top-6 left-1/2 transform -translate-x-1/2 bg-primary-900 hover:bg-primary-800 text-white p-4 rounded-full shadow-lg transition-all duration-300 z-10"
        whileHover={{ scale: 1.1, y: -2 }}
        whileTap={{ scale: 0.9 }}
      >
        <ArrowUp className="w-6 h-6" />
      </motion.button>

      <div className="container mx-auto px-6 sm:px-8 pt-20 sm:pt-24 pb-8 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 sm:gap-12">
          {/* Company Info */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="lg:col-span-1"
          >
            <div className="flex items-center space-x-3 mb-6">
              <div className="bg-white rounded-xl p-2 shadow-md flex items-center justify-center h-14 w-14">
                <img
                  src="/images/everyday_logo_sm.png"
                  alt="Everyday Value Jamaica"
                  className="h-10 w-auto"
                />
              </div>
              <div>
                <h3 className="text-lg sm:text-xl font-heading font-bold">EVERYDAY</h3>
                <h3 className="text-lg sm:text-xl font-heading font-bold text-accent-400">VALUE</h3>
              </div>
            </div>
            
            <p className="text-neutral-300 leading-relaxed mb-6 text-sm sm:text-base font-medium">
              Best Brands. Best Price. Best Choice.
            </p>

            {/* Social Links */}
            <div className="flex space-x-4">
              {socialLinks.map((social, index) => (
                <motion.a
                  key={index}
                  href={social.href}
                  className="bg-gray-100 hover:bg-accent-500 hover:text-white p-3 rounded-full transition-all duration-300"
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.9 }}
                  aria-label={social.label}
                >
                  <social.icon className="w-5 h-5" />
                </motion.a>
              ))}
            </div>
          </motion.div>

          {/* Office Information */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="lg:col-span-1"
          >
            <h4 className="text-base sm:text-lg font-heading font-bold mb-6">Office Hours</h4>
            <div className="space-y-2">
              {officeHours.schedule.map((line, lineIndex) => (
                <p key={lineIndex} className="text-neutral-300 text-xs sm:text-sm">
                  {line}
                </p>
              ))}
            </div>
          </motion.div>

          {/* Navigation */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="lg:col-span-1"
          >
            <h4 className="text-base sm:text-lg font-heading font-bold mb-6">Navigation</h4>
            <nav className="space-y-3">
              {navigationLinks.map((link) => (
                <button
                  key={link.id}
                  onClick={() => onNavigate(link.id)}
                  className="block text-neutral-300 hover:text-accent-400 transition-colors duration-200 text-sm sm:text-base text-left"
                >
                  &gt; {link.label}
                </button>
              ))}
            </nav>
          </motion.div>

          {/* Get In Touch */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="lg:col-span-1"
          >
            <h4 className="text-base sm:text-lg font-heading font-bold mb-6">Get In Touch</h4>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <MapPin className="w-5 h-5 text-accent-400 mt-1 flex-shrink-0" />
                <div>
                  <p className="text-neutral-300 text-sm sm:text-base">
                    12 Colbeck Avenue,<br />
                    Kingston 10,<br />
                    Jamaica W. I.
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <Mail className="w-5 h-5 text-accent-400 flex-shrink-0" />
                <a 
                  href="mailto:<EMAIL>"
                  className="text-neutral-300 hover:text-accent-400 transition-colors text-sm sm:text-base"
                >
                  <EMAIL>
                </a>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center space-x-3">
                  <Phone className="w-5 h-5 text-accent-400 flex-shrink-0" />
                  <a
                    href="tel:+1876-433-1359"
                    className="text-neutral-300 hover:text-accent-400 transition-colors text-sm sm:text-base"
                  >
                    (*************
                  </a>
                </div>
                <div className="flex items-center space-x-3">
                  <Phone className="w-5 h-5 text-accent-400 flex-shrink-0" />
                  <a 
                    href="tel:+1876-665-0322" 
                    className="text-neutral-300 hover:text-accent-400 transition-colors text-sm sm:text-base"
                  >
                    (*************
                  </a>
                </div>
                <div className="flex items-center space-x-3">
                  <Phone className="w-5 h-5 text-accent-400 flex-shrink-0" />
                  <a 
                    href="tel:+1876-622-2920" 
                    className="text-neutral-300 hover:text-accent-400 transition-colors text-sm sm:text-base"
                  >
                    (*************
                  </a>
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Bottom Bar */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="border-t border-white/10 mt-12 sm:mt-16 pt-8"
        >
          <div className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
            <div className="flex items-center space-x-2 text-neutral-400 text-sm">
              <span>&copy; 2025 Everyday Value Jamaica. All rights reserved.</span>
            </div>
            
            <div className="flex flex-wrap items-center justify-center space-x-4 sm:space-x-6 text-sm">
              <button className="text-neutral-400 hover:text-accent-400 transition-colors">
                Privacy Policy
              </button>
              <button className="text-neutral-400 hover:text-accent-400 transition-colors">
                Terms of Service
              </button>
              <button className="text-neutral-400 hover:text-accent-400 transition-colors">
                Cookie Policy
              </button>
            </div>
            
            <div className="flex items-center space-x-2 text-neutral-400 text-sm">
              <span>Made with</span>
              <Heart className="w-4 h-4 text-red-500" />
              <span>in Jamaica</span>
            </div>
          </div>
        </motion.div>
      </div>
    </footer>
  );
};

export default Footer;