import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import {
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { brands } from '../data/brands';

const FeaturedBrandsCarousel: React.FC = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // Get featured brands from existing data
  const featuredBrands = brands.filter(brand => brand.featured);

  const [translateX, setTranslateX] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const [currentSlide, setCurrentSlide] = useState(0);

  // Create multiple copies for seamless infinite scroll
  const duplicatedBrands = [...featuredBrands, ...featuredBrands, ...featuredBrands];
  const totalSlides = featuredBrands.length;

  // Continuous scroll effect
  useEffect(() => {
    if (isPaused) return;

    const interval = setInterval(() => {
      setTranslateX(prev => {
        const newTranslateX = prev - 0.1; // Move 0.1% every 50ms for smooth motion
        // Reset when we've moved one full set of brands
        if (Math.abs(newTranslateX) >= (100 / 3)) {
          return 0;
        }
        return newTranslateX;
      });
    }, 50); // Update every 50ms for smooth continuous motion

    return () => clearInterval(interval);
  }, [isPaused]);

  const nextSlide = () => {
    setCurrentSlide(prev => (prev + 1) % totalSlides);
    setTranslateX(prev => {
      const newTranslateX = prev - (100 / 3); // Move by one brand width
      if (Math.abs(newTranslateX) >= (100 / 3) * featuredBrands.length) {
        return 0;
      }
      return newTranslateX;
    });
    setIsPaused(true);
    setTimeout(() => setIsPaused(false), 3000);
  };

  const prevSlide = () => {
    setCurrentSlide(prev => (prev - 1 + totalSlides) % totalSlides);
    setTranslateX(prev => {
      const newTranslateX = prev + (100 / 3); // Move by one brand width
      if (newTranslateX > 0) {
        return -((100 / 3) * (featuredBrands.length - 1));
      }
      return newTranslateX;
    });
    setIsPaused(true);
    setTimeout(() => setIsPaused(false), 3000);
  };

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
    setTranslateX(-(100 / 3) * index); // Jump directly to the slide
    setIsPaused(true);
    setTimeout(() => setIsPaused(false), 3000);
  };

  return (
    <section className="py-16 sm:py-20 lg:py-24 bg-gradient-to-r from-accent-600 via-accent-500 to-accent-400 relative overflow-hidden">
      <div className="container mx-auto px-6 sm:px-8 relative z-10">
        <motion.div
          ref={ref}
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-16 sm:mb-20"
        >
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-heading font-bold text-white mb-6 sm:mb-8">
            Featured Brands
          </h2>
          <p className="text-lg sm:text-xl text-white/90 max-w-4xl mx-auto leading-relaxed px-4">
            Discover the trusted brands that make EverydayValue Jamaica's preferred distribution partner.
          </p>
        </motion.div>

        {/* Featured Brands Carousel */}
        <div className="relative max-w-6xl mx-auto">
          <div className="overflow-hidden">
            <div
              className="flex transition-transform ease-linear"
              onMouseEnter={() => setIsPaused(true)}
              onMouseLeave={() => setIsPaused(false)}
              style={{
                transform: `translateX(${translateX}%)`,
                width: `${duplicatedBrands.length * (100 / 5)}%`
              }}
            >
              {duplicatedBrands.map((brand, index) => (
                <div
                  key={`${brand.id}-${index}`}
                  className="flex-shrink-0 px-4"
                  style={{ width: `${100 / duplicatedBrands.length}%` }}
                >
                  <div className="h-16 flex items-center justify-center group hover:scale-105 transition-all duration-300">
                    <img
                      src={brand.logo}
                      alt={brand.name}
                      className="max-w-full max-h-full object-contain filter drop-shadow-lg"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        const fallback = target.parentElement?.querySelector('.fallback-text') as HTMLElement;
                        if (fallback) fallback.style.display = 'flex';
                      }}
                    />
                    <div className="fallback-text hidden w-full h-full items-center justify-center text-white font-bold text-lg">
                      {brand.name}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Navigation Arrows */}
          <button
            onClick={prevSlide}
            className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-4 bg-white/20 backdrop-blur-sm rounded-full p-3 hover:bg-white/30 transition-all duration-300 text-white hover:text-white z-10"
            aria-label="Previous brands"
          >
            <ChevronLeft className="w-6 h-6" />
          </button>

          <button
            onClick={nextSlide}
            className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-4 bg-white/20 backdrop-blur-sm rounded-full p-3 hover:bg-white/30 transition-all duration-300 text-white hover:text-white z-10"
            aria-label="Next brands"
          >
            <ChevronRight className="w-6 h-6" />
          </button>

          {/* Carousel Indicators */}
          <div className="flex justify-center mt-12 space-x-2">
            {Array.from({ length: totalSlides }).map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentSlide
                    ? 'bg-white scale-125'
                    : 'bg-white/40 hover:bg-white/60'
                }`}
                aria-label={`Go to brand slide ${index + 1}`}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeaturedBrandsCarousel;