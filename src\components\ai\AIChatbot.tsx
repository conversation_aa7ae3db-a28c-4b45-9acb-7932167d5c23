import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  MessageCircle, 
  X, 
  Send, 
  Bot, 
  User, 
  Package, 
  Building, 
  Tag,
  ArrowRight,
  Sparkles,
  Phone,
  MessageSquare
} from 'lucide-react';
import { products, brands, categories } from '../../data/brands';

interface Message {
  id: string;
  type: 'user' | 'bot';
  content: string;
  timestamp: Date;
  suggestions?: ChatSuggestion[];
}

interface ChatSuggestion {
  text: string;
  action: () => void;
  icon?: React.ReactNode;
  type?: 'whatsapp' | 'navigation' | 'action';
}

const AIChatbot: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // WhatsApp Business Number (replace with actual number)
  const whatsappNumber = "***********"; // Everyday Value Jamaica WhatsApp number

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Focus input when chat opens
  useEffect(() => {
    if (isOpen) {
      setTimeout(() => inputRef.current?.focus(), 100);
    }
  }, [isOpen]);

  // Initialize with welcome message
  useEffect(() => {
    if (messages.length === 0) {
      const welcomeMessage: Message = {
        id: '1',
        type: 'bot',
        content: "Hi! I'm your Everyday Value Jamaica assistant. I can help you find products, explore brands, or connect you with our team. What are you looking for today?",
        timestamp: new Date(),
        suggestions: [
          {
            text: "Show me all ADA products",
            action: () => handleSuggestionClick("Show me all ADA products"),
            icon: <Building className="w-4 h-4" />,
            type: 'navigation'
          },
          {
            text: "Find snacks with fruit",
            action: () => handleSuggestionClick("Find snacks with fruit"),
            icon: <Package className="w-4 h-4" />,
            type: 'navigation'
          },
          {
            text: "Chat on WhatsApp",
            action: () => openWhatsApp("Hi! I'm interested in learning more about EverydayValueJamaica products and services."),
            icon: <MessageSquare className="w-4 h-4" />,
            type: 'whatsapp'
          }
        ]
      };
      setMessages([welcomeMessage]);
    }
  }, []);

  const openWhatsApp = (message: string) => {
    const encodedMessage = encodeURIComponent(message);
    const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${encodedMessage}`;
    window.open(whatsappUrl, '_blank');
  };

  const handleSuggestionClick = (suggestion: string) => {
    setInputValue(suggestion);
    handleSendMessage(suggestion);
  };

  const processUserQuery = (query: string): { response: string; suggestions?: ChatSuggestion[] } => {
    const lowerQuery = query.toLowerCase();

    // WhatsApp/Contact related queries
    if (lowerQuery.includes('whatsapp') || lowerQuery.includes('chat') || lowerQuery.includes('talk') || lowerQuery.includes('speak')) {
      return {
        response: "I'd be happy to connect you with our team on WhatsApp! You can chat directly with our customer service representatives for personalized assistance, order inquiries, or any questions about our products and services.",
        suggestions: [
          {
            text: "Chat on WhatsApp",
            action: () => openWhatsApp("Hi! I'd like to speak with someone about EverydayValueJamaica products and services."),
            icon: <MessageSquare className="w-4 h-4" />,
            type: 'whatsapp'
          },
          {
            text: "Call Us",
            action: () => window.location.href = 'tel:+***********',
            icon: <Phone className="w-4 h-4" />,
            type: 'action'
          },
          {
            text: "Contact Information",
            action: () => window.location.href = '/contact',
            icon: <ArrowRight className="w-4 h-4" />,
            type: 'navigation'
          }
        ]
      };
    }

    // Help/Support queries
    if (lowerQuery.includes('help') || lowerQuery.includes('support') || lowerQuery.includes('assistance') || lowerQuery.includes('problem')) {
      return {
        response: "I'm here to help! I can assist you with finding products, learning about our brands, or connecting you with our support team. For complex inquiries or immediate assistance, our WhatsApp support is available during business hours.",
        suggestions: [
          {
            text: "Get WhatsApp Support",
            action: () => openWhatsApp("Hi! I need help with: " + query),
            icon: <MessageSquare className="w-4 h-4" />,
            type: 'whatsapp'
          },
          {
            text: "Browse Products",
            action: () => window.location.href = '/products',
            icon: <Package className="w-4 h-4" />,
            type: 'navigation'
          },
          {
            text: "View Services",
            action: () => window.location.href = '/services',
            icon: <Building className="w-4 h-4" />,
            type: 'navigation'
          }
        ]
      };
    }

    // Order/Purchase related queries
    if (lowerQuery.includes('order') || lowerQuery.includes('buy') || lowerQuery.includes('purchase') || lowerQuery.includes('price') || lowerQuery.includes('cost')) {
      return {
        response: "For orders, pricing, and purchase inquiries, our sales team can provide you with detailed information and personalized quotes. They're available on WhatsApp during business hours to assist with your specific needs.",
        suggestions: [
          {
            text: "WhatsApp Sales Team",
            action: () => openWhatsApp("Hi! I'm interested in placing an order. Can you help me with pricing and availability?"),
            icon: <MessageSquare className="w-4 h-4" />,
            type: 'whatsapp'
          },
          {
            text: "View Our Services",
            action: () => window.location.href = '/services',
            icon: <ArrowRight className="w-4 h-4" />,
            type: 'navigation'
          },
          {
            text: "Contact Sales",
            action: () => window.location.href = '/contact',
            icon: <Building className="w-4 h-4" />,
            type: 'navigation'
          }
        ]
      };
    }

    // Brand-specific queries
    if (lowerQuery.includes('ada')) {
      const adaProducts = products.filter(p => p.brand.toLowerCase() === 'ada');
      return {
        response: `I found ${adaProducts.length} ADA products in our catalog! ADA offers premium household supplies, beverages, and everyday essentials. Would you like to explore specific categories or get more details from our team?`,
        suggestions: [
          {
            text: "View All ADA Products",
            action: () => window.location.href = '/products?brand=ADA',
            icon: <ArrowRight className="w-4 h-4" />,
            type: 'navigation'
          },
          {
            text: "Ask About ADA on WhatsApp",
            action: () => openWhatsApp("Hi! I'd like to know more about ADA products. Can you provide details about availability and pricing?"),
            icon: <MessageSquare className="w-4 h-4" />,
            type: 'whatsapp'
          },
          {
            text: "ADA Beverages",
            action: () => window.location.href = '/products?brand=ADA&category=beverages',
            icon: <Package className="w-4 h-4" />,
            type: 'navigation'
          }
        ]
      };
    }

    if (lowerQuery.includes('nicies')) {
      const niciesProducts = products.filter(p => p.brand.toLowerCase() === 'nicies');
      return {
        response: `Nicies has ${niciesProducts.length} delicious products! They specialize in quality beverages and confectionery. From refreshing drinks to sweet treats, Nicies brings authentic Caribbean flavors.`,
        suggestions: [
          {
            text: "View Nicies Products",
            action: () => window.location.href = '/products?brand=Nicies',
            icon: <ArrowRight className="w-4 h-4" />,
            type: 'navigation'
          },
          {
            text: "WhatsApp About Nicies",
            action: () => openWhatsApp("Hi! I'm interested in Nicies products. Can you tell me more about their beverages and availability?"),
            icon: <MessageSquare className="w-4 h-4" />,
            type: 'whatsapp'
          },
          {
            text: "Nicies Beverages",
            action: () => window.location.href = '/products?brand=Nicies&category=beverages',
            icon: <Package className="w-4 h-4" />,
            type: 'navigation'
          }
        ]
      };
    }

    if (lowerQuery.includes('st bess') || lowerQuery.includes('st. bess')) {
      const stBessProducts = products.filter(p => p.brand.toLowerCase().includes('st bess'));
      return {
        response: `St Bess offers ${stBessProducts.length} premium food products! They specialize in authentic Jamaican spices and traditional treats, all made locally with natural ingredients.`,
        suggestions: [
          {
            text: "View St Bess Products",
            action: () => window.location.href = '/products?brand=St Bess',
            icon: <ArrowRight className="w-4 h-4" />,
            type: 'navigation'
          },
          {
            text: "Ask About St Bess",
            action: () => openWhatsApp("Hi! I'm interested in St Bess Jamaican spices and food products. Can you provide more information?"),
            icon: <MessageSquare className="w-4 h-4" />,
            type: 'whatsapp'
          },
          {
            text: "Jamaican Spices",
            action: () => window.location.href = '/products?search=spice',
            icon: <Package className="w-4 h-4" />,
            type: 'navigation'
          }
        ]
      };
    }

    // Category-specific queries
    if (lowerQuery.includes('snack') || lowerQuery.includes('fruit')) {
      const snackProducts = products.filter(p => 
        p.category === 'snacks' || 
        p.category === 'confectionery' ||
        p.tags.some(tag => tag.includes('fruit') || tag.includes('snack'))
      );
      return {
        response: `I found ${snackProducts.length} delicious snacks and treats! We have everything from crispy chips to fruity confectionery. Perfect for any time of day!`,
        suggestions: [
          {
            text: "View All Snacks",
            action: () => window.location.href = '/products?category=snacks',
            icon: <ArrowRight className="w-4 h-4" />,
            type: 'navigation'
          },
          {
            text: "WhatsApp About Snacks",
            action: () => openWhatsApp("Hi! I'm looking for information about your snack products. Can you help me with availability and bulk pricing?"),
            icon: <MessageSquare className="w-4 h-4" />,
            type: 'whatsapp'
          },
          {
            text: "Show Confectionery",
            action: () => window.location.href = '/products?category=confectionery',
            icon: <Package className="w-4 h-4" />,
            type: 'navigation'
          }
        ]
      };
    }

    if (lowerQuery.includes('beverage') || lowerQuery.includes('drink') || lowerQuery.includes('juice')) {
      const beverageProducts = products.filter(p => p.category === 'beverages');
      return {
        response: `We have ${beverageProducts.length} refreshing beverages! From tropical fruit flavors to classic sodas, our drink selection covers all your hydration needs.`,
        suggestions: [
          {
            text: "View All Beverages",
            action: () => window.location.href = '/products?category=beverages',
            icon: <ArrowRight className="w-4 h-4" />,
            type: 'navigation'
          },
          {
            text: "WhatsApp About Beverages",
            action: () => openWhatsApp("Hi! I'm interested in your beverage selection. Can you provide information about brands and bulk ordering?"),
            icon: <MessageSquare className="w-4 h-4" />,
            type: 'whatsapp'
          },
          {
            text: "Fruit Juices",
            action: () => window.location.href = '/products?search=juice',
            icon: <Package className="w-4 h-4" />,
            type: 'navigation'
          }
        ]
      };
    }

    if (lowerQuery.includes('household') || lowerQuery.includes('cleaning') || lowerQuery.includes('home')) {
      const householdProducts = products.filter(p => p.category === 'household');
      return {
        response: `Our household essentials section has ${householdProducts.length} products to keep your home clean and organized. Quality supplies for every household need!`,
        suggestions: [
          {
            text: "View Household Items",
            action: () => window.location.href = '/products?category=household',
            icon: <ArrowRight className="w-4 h-4" />,
            type: 'navigation'
          },
          {
            text: "WhatsApp About Household",
            action: () => openWhatsApp("Hi! I need information about your household and cleaning products. Can you help with product details and pricing?"),
            icon: <MessageSquare className="w-4 h-4" />,
            type: 'whatsapp'
          },
          {
            text: "Cleaning Products",
            action: () => window.location.href = '/products?search=cleaning',
            icon: <Package className="w-4 h-4" />,
            type: 'navigation'
          }
        ]
      };
    }

    if (lowerQuery.includes('food') || lowerQuery.includes('spice') || lowerQuery.includes('cooking')) {
      const foodProducts = products.filter(p => p.category === 'food');
      return {
        response: `We have ${foodProducts.length} quality food products! From authentic Jamaican spices to pantry essentials, everything you need for delicious cooking.`,
        suggestions: [
          {
            text: "View Food Products",
            action: () => window.location.href = '/products?category=food',
            icon: <ArrowRight className="w-4 h-4" />,
            type: 'navigation'
          },
          {
            text: "WhatsApp About Food",
            action: () => openWhatsApp("Hi! I'm interested in your food products and Jamaican spices. Can you provide more details about what's available?"),
            icon: <MessageSquare className="w-4 h-4" />,
            type: 'whatsapp'
          },
          {
            text: "Jamaican Spices",
            action: () => window.location.href = '/products?search=jamaican',
            icon: <Package className="w-4 h-4" />,
            type: 'navigation'
          }
        ]
      };
    }

    // General navigation queries
    if (lowerQuery.includes('brand') || lowerQuery.includes('manufacturer')) {
      return {
        response: `We work with ${brands.length} premium brands! Each brand is carefully selected for quality and reliability. Explore our brand portfolio to discover your favorites.`,
        suggestions: [
          {
            text: "View All Brands",
            action: () => window.location.href = '/brands',
            icon: <Building className="w-4 h-4" />,
            type: 'navigation'
          },
          {
            text: "WhatsApp About Brands",
            action: () => openWhatsApp("Hi! I'd like to learn more about the brands you distribute. Can you provide information about partnerships and product lines?"),
            icon: <MessageSquare className="w-4 h-4" />,
            type: 'whatsapp'
          },
          {
            text: "Featured Brands",
            action: () => window.location.href = '/brands?featured=true',
            icon: <Sparkles className="w-4 h-4" />,
            type: 'navigation'
          }
        ]
      };
    }

    if (lowerQuery.includes('service') || lowerQuery.includes('distribution') || lowerQuery.includes('delivery')) {
      return {
        response: "We offer comprehensive distribution services including marketing & sales support, delivery & merchandising, and warehousing & customer care. Our services are designed to help your brand succeed across Jamaica!",
        suggestions: [
          {
            text: "Learn About Services",
            action: () => window.location.href = '/services',
            icon: <ArrowRight className="w-4 h-4" />,
            type: 'navigation'
          },
          {
            text: "Distribution Manufacturing",
            action: () => window.location.href = '/distribution-manufacturing',
            icon: <ArrowRight className="w-4 h-4" />,
            type: 'navigation'
          },
          {
            text: "WhatsApp About Services",
            action: () => openWhatsApp("Hi! I'm interested in your distribution services. Can you provide information about partnerships and service packages?"),
            icon: <MessageSquare className="w-4 h-4" />,
            type: 'whatsapp'
          }
        ]
      };
    }

    // Manufacturing related queries - redirect to distribution services
    if (lowerQuery.includes('manufacturing') || lowerQuery.includes('contract') || lowerQuery.includes('private label') || lowerQuery.includes('co-packing')) {
      return {
        response: "EverydayValue specializes in distribution services, connecting quality brands with retailers across Jamaica. We focus on efficient distribution, logistics, and supply chain management rather than manufacturing. Let me help you learn about our distribution services!",
        suggestions: [
          {
            text: "Our Distribution Services",
            action: () => window.location.href = '/what-we-do',
            icon: <ArrowRight className="w-4 h-4" />,
            type: 'navigation'
          },

          {
            text: "What We Do",
            action: () => window.location.href = '/what-we-do',
            icon: <Building className="w-4 h-4" />,
            type: 'navigation'
          }
        ]
      };
    }

    // About/Company related queries
    if (lowerQuery.includes('about') || lowerQuery.includes('company') || lowerQuery.includes('who are you') || lowerQuery.includes('history')) {
      return {
        response: "EverydayValueJamaica has been Jamaica's premier distribution partner for over 20 years! We're committed to connecting communities with quality brands through excellence, integrity, and innovation. Learn more about our story and values.",
        suggestions: [
          {
            text: "Who We Are",
            action: () => window.location.href = '/who-we-are',
            icon: <ArrowRight className="w-4 h-4" />,
            type: 'navigation'
          },
          {
            text: "What We Do",
            action: () => window.location.href = '/what-we-do',
            icon: <ArrowRight className="w-4 h-4" />,
            type: 'navigation'
          },
          {
            text: "WhatsApp About Company",
            action: () => openWhatsApp("Hi! I'd like to learn more about EverydayValueJamaica's history and services."),
            icon: <MessageSquare className="w-4 h-4" />,
            type: 'whatsapp'
          }
        ]
      };
    }

    if (lowerQuery.includes('contact') || lowerQuery.includes('phone') || lowerQuery.includes('email')) {
      return {
        response: "You can reach us at (************* <NAME_EMAIL>. Our office is located at 12 Colbeck Avenue, Kingston 10. We're here Monday-Friday, 9 AM to 5 PM! For instant messaging, try our WhatsApp.",
        suggestions: [
          {
            text: "WhatsApp Us Now",
            action: () => openWhatsApp("Hi! I'd like to get in touch with your team."),
            icon: <MessageSquare className="w-4 h-4" />,
            type: 'whatsapp'
          },
          {
            text: "Call Us",
            action: () => window.location.href = 'tel:+***********',
            icon: <Phone className="w-4 h-4" />,
            type: 'action'
          },
          {
            text: "Contact Information",
            action: () => window.location.href = '/contact',
            icon: <ArrowRight className="w-4 h-4" />,
            type: 'navigation'
          }
        ]
      };
    }

    // Default fallback response
    return {
      response: "I'd be happy to help you find what you're looking for! You can search for specific products, explore our brands, learn about our company, or connect with our team on WhatsApp for personalized assistance. What interests you most?",
      suggestions: [
        {
          text: "Chat on WhatsApp",
          action: () => openWhatsApp("Hi! I have a question about Everyday Value Jamaica products and services."),
          icon: <MessageSquare className="w-4 h-4" />,
          type: 'whatsapp'
        },
        {
          text: "Who We Are",
          action: () => window.location.href = '/who-we-are',
          icon: <Building className="w-4 h-4" />,
          type: 'navigation'
        },
        {
          text: "What We Do",
          action: () => window.location.href = '/what-we-do',
          icon: <Package className="w-4 h-4" />,
          type: 'navigation'
        }
      ]
    };
  };

  const handleSendMessage = async (messageText?: string) => {
    const text = messageText || inputValue.trim();
    if (!text) return;

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: text,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsTyping(true);

    // Simulate AI processing delay
    setTimeout(() => {
      const { response, suggestions } = processUserQuery(text);
      
      const botMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'bot',
        content: response,
        timestamp: new Date(),
        suggestions
      };

      setMessages(prev => [...prev, botMessage]);
      setIsTyping(false);
    }, 1000 + Math.random() * 1000); // Random delay between 1-2 seconds
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const getSuggestionStyle = (type?: string) => {
    switch (type) {
      case 'whatsapp':
        return 'bg-green-50 border-green-200 hover:bg-green-100 text-green-800';
      case 'action':
        return 'bg-blue-50 border-blue-200 hover:bg-blue-100 text-blue-800';
      default:
        return 'bg-white border-neutral-200 hover:bg-neutral-50 text-neutral-800';
    }
  };

  return (
    <>
      {/* Chat Toggle Button */}
      <motion.button
        onClick={() => setIsOpen(true)}
        className={`fixed bottom-6 right-6 z-40 bg-accent-500 hover:bg-accent-600 text-white p-4 rounded-full shadow-lg transition-all duration-300 ${isOpen ? 'scale-0' : 'scale-100'}`}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        initial={{ scale: 0 }}
        animate={{ scale: isOpen ? 0 : 1 }}
      >
        <MessageCircle className="w-6 h-6" />
        <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
      </motion.button>

      {/* Chat Window */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 20 }}
            className="fixed bottom-6 right-6 z-50 w-96 h-[600px] bg-white rounded-2xl shadow-2xl border border-neutral-200 flex flex-col overflow-hidden"
          >
            {/* Header */}
            <div className="bg-gradient-to-r from-accent-500 to-accent-600 p-4 text-white">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                    <Bot className="w-5 h-5" />
                  </div>
                  <div>
                    <h3 className="font-semibold">EverydayValue Assistant</h3>
                    <p className="text-xs text-white/80">Always here to help</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => openWhatsApp("Hi! I'd like to chat with your team.")}
                    className="p-2 hover:bg-white/20 rounded-lg transition-colors"
                    title="Chat on WhatsApp"
                  >
                    <MessageSquare className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => setIsOpen(false)}
                    className="p-1 hover:bg-white/20 rounded-lg transition-colors"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>
              </div>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div className={`max-w-[80%] ${message.type === 'user' ? 'order-2' : 'order-1'}`}>
                    <div
                      className={`p-3 rounded-2xl ${
                        message.type === 'user'
                          ? 'bg-accent-500 text-white'
                          : 'bg-neutral-100 text-neutral-800'
                      }`}
                    >
                      <p className="text-sm leading-relaxed">{message.content}</p>
                    </div>
                    
                    {/* Suggestions */}
                    {message.suggestions && (
                      <div className="mt-3 space-y-2">
                        {message.suggestions.map((suggestion, index) => (
                          <motion.button
                            key={index}
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: index * 0.1 }}
                            onClick={suggestion.action}
                            className={`w-full text-left p-2 border rounded-lg transition-colors text-sm flex items-center space-x-2 ${getSuggestionStyle(suggestion.type)}`}
                          >
                            {suggestion.icon}
                            <span>{suggestion.text}</span>
                          </motion.button>
                        ))}
                      </div>
                    )}
                    
                    <div className={`flex items-center mt-1 space-x-2 ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                      <div className={`w-6 h-6 rounded-full flex items-center justify-center ${message.type === 'user' ? 'bg-accent-600' : 'bg-neutral-200'}`}>
                        {message.type === 'user' ? <User className="w-3 h-3 text-white" /> : <Bot className="w-3 h-3 text-neutral-600" />}
                      </div>
                      <span className="text-xs text-neutral-500">
                        {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </span>
                    </div>
                  </div>
                </div>
              ))}

              {/* Typing Indicator */}
              {isTyping && (
                <div className="flex justify-start">
                  <div className="bg-neutral-100 p-3 rounded-2xl">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-neutral-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-neutral-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-neutral-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                  </div>
                </div>
              )}
              
              <div ref={messagesEndRef} />
            </div>

            {/* Input */}
            <div className="p-4 border-t border-neutral-200">
              <div className="flex space-x-2">
                <input
                  ref={inputRef}
                  type="text"
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Ask me anything..."
                  className="flex-1 px-3 py-2 border border-neutral-200 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-transparent transition-all text-sm"
                />
                <button
                  onClick={() => handleSendMessage()}
                  disabled={!inputValue.trim()}
                  className="bg-accent-500 hover:bg-accent-600 disabled:bg-neutral-300 text-white p-2 rounded-lg transition-colors"
                >
                  <Send className="w-4 h-4" />
                </button>
              </div>
              
              {/* Quick WhatsApp Access */}
              <div className="mt-2 text-center">
                <button
                  onClick={() => openWhatsApp("Hi! I'd like to speak with someone from EverydayValueJamaica.")}
                  className="text-xs text-green-600 hover:text-green-700 transition-colors flex items-center justify-center space-x-1"
                >
                  <MessageSquare className="w-3 h-3" />
                  <span>Need immediate help? Chat on WhatsApp</span>
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default AIChatbot;