import React, { useState, useEffect, useMemo, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { Calendar, User, ArrowRight, Search, Tag, X, Share2, Facebook, Twitter, Linkedin, Heart, Volume2, Globe } from 'lucide-react';
import SEO from '../components/common/SEO';

interface NewsArticle {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  author: string;
  date: string;
  category: string;
  image: string;
  tags: string[];
  featured: boolean;
}

// Helper: get gradient based on time of day
function getDynamicGradient() {
  const hour = new Date().getHours();
  if (hour >= 6 && hour < 18) {
    // Day: blue-accent
    return 'linear-gradient(120deg, #1e3a8a 0%, #2563eb 50%, #38bdf8 100%)';
  } else {
    // Night: dark-accent
    return 'linear-gradient(120deg, #0f172a 0%, #1e293b 50%, #2563eb 100%)';
  }
}

const News: React.FC = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedArticle, setSelectedArticle] = useState<NewsArticle | null>(null);
  const [gradient, setGradient] = useState(getDynamicGradient());
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [viewMode, setViewMode] = useState<'card' | 'list'>('card');
  const [bookmarked, setBookmarked] = useState<{[id: string]: boolean}>({});
  const [ttsLang, setTtsLang] = useState<'en' | 'es'>('en');
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [showTrending, setShowTrending] = useState(true);

  const articles: NewsArticle[] = [
    {
      id: '1',
      title: 'Everyday Value Jamaica Expands Distribution Network Across Jamaica',
      excerpt: 'We are excited to announce the expansion of our distribution network to better serve communities across Jamaica with faster, more reliable delivery services.',
      content: `
        <p>Everyday Value Jamaica is proud to announce a significant expansion of our distribution network across Jamaica, marking a new milestone in our commitment to serving communities islandwide. This expansion represents our largest infrastructure investment to date and will dramatically improve our ability to deliver quality products to retailers and consumers across the island.</p>
        
        <p>The expansion includes the opening of three new distribution centers in Montego Bay, Spanish Town, and May Pen, strategically located to optimize delivery routes and reduce transit times. These state-of-the-art facilities are equipped with modern inventory management systems and climate-controlled storage to ensure product quality.</p>
        
        <p>"This expansion is a testament to our commitment to Jamaica and our belief in the potential of local businesses," said our CEO. "By bringing our services closer to communities across the island, we're not just improving logistics – we're creating jobs and supporting local economic development."</p>
        
        <p>The new facilities will create over 150 new jobs across various departments, including warehouse operations, logistics, customer service, and management positions. We're actively recruiting from local communities and providing comprehensive training programs to ensure our team members have the skills needed to excel in their roles.</p>
        
        <p>This expansion will enable us to reduce delivery times by up to 40% for many locations and expand our reach to previously underserved areas. We're particularly excited about the opportunities this creates for small retailers in rural communities who will now have better access to our product portfolio.</p>
      `,
      author: 'Everyday Value Jamaica Team',
      date: '2025-01-15',
      category: 'Company News',
      image: 'https://images.pexels.com/photos/4481259/pexels-photo-4481259.jpeg?auto=compress&cs=tinysrgb&w=800&h=400&fit=crop',
      tags: ['expansion', 'distribution', 'jobs', 'infrastructure'],
      featured: true
    },
    {
      id: '2',
      title: 'New Partnership with Local Jamaican Brands',
      excerpt: 'We are proud to announce new partnerships with several local Jamaican brands, strengthening our commitment to supporting homegrown businesses.',
      content: `
        <p>Everyday Value Jamaica is excited to announce new partnerships with several prominent local Jamaican brands, further strengthening our commitment to supporting homegrown businesses and promoting "Brand Jamaica" across the island and beyond.</p>
        
        <p>These partnerships include collaborations with established local manufacturers in the food and beverage, personal care, and household products sectors. By adding these authentic Jamaican products to our distribution portfolio, we're helping local businesses reach new markets while giving consumers greater access to quality local alternatives.</p>
        
        <p>Our new partners include several award-winning companies that have built strong reputations for quality and innovation. These brands represent the best of Jamaican entrepreneurship and manufacturing excellence, and we're honored to support their growth and expansion.</p>
        
        <p>The partnership program includes comprehensive marketing support, optimized distribution strategies, and access to our extensive retail network. We're also providing business development assistance to help these brands scale their operations and improve their market positioning.</p>
        
        <p>"Supporting local businesses isn't just good business – it's the right thing to do for Jamaica," said our Head of Business Development. "These partnerships create a multiplier effect that benefits the entire economy, from farmers and manufacturers to retailers and consumers."</p>
      `,
      author: 'Marketing Team',
      date: '2025-01-10',
      category: 'Partnerships',
      image: 'https://images.pexels.com/photos/3184291/pexels-photo-3184291.jpeg?auto=compress&cs=tinysrgb&w=800&h=400&fit=crop',
      tags: ['partnerships', 'local brands', 'Jamaica', 'business development'],
      featured: true
    },
    {
      id: '3',
      title: 'Sustainability Initiative: Going Green with Our Fleet',
      excerpt: 'Learn about our new sustainability initiative aimed at reducing our environmental impact through fleet modernization and eco-friendly practices.',
      content: `
        <p>EverydayValueJamaica is launching a comprehensive sustainability initiative focused on reducing our environmental impact and promoting eco-friendly practices throughout our operations. This initiative represents our commitment to environmental stewardship and sustainable business practices.</p>
        
        <p>The centerpiece of this initiative is the modernization of our delivery fleet with more fuel-efficient vehicles and the introduction of electric delivery trucks for urban routes. Over the next two years, we plan to replace 60% of our fleet with vehicles that produce significantly lower emissions.</p>
        
        <p>We're also implementing route optimization technology that uses AI to plan the most efficient delivery routes, reducing fuel consumption and minimizing our carbon footprint. Early testing has shown potential fuel savings of up to 25% on optimized routes.</p>
        
        <p>Beyond transportation, we're introducing sustainable packaging options for our partners and implementing comprehensive recycling programs at all our facilities. We're also exploring renewable energy options for our warehouses and distribution centers.</p>
        
        <p>This initiative aligns with Jamaica's national environmental goals and demonstrates our commitment to being a responsible corporate citizen. We believe that sustainable business practices are not just good for the environment – they're essential for long-term business success.</p>
      `,
      author: 'Operations Team',
      date: '2025-01-05',
      category: 'Sustainability',
      image: 'https://images.pexels.com/photos/9800029/pexels-photo-9800029.jpeg?auto=compress&cs=tinysrgb&w=800&h=400&fit=crop',
      tags: ['sustainability', 'environment', 'fleet', 'green initiative'],
      featured: false
    },
    {
      id: '4',
      title: 'Employee Spotlight: Excellence in Customer Service',
      excerpt: 'Meet some of our outstanding team members who go above and beyond to deliver exceptional customer service every day.',
      content: `
        <p>At Everyday Value Jamaica, our success is built on the dedication and excellence of our team members. This month, we're highlighting several outstanding employees who exemplify our commitment to exceptional customer service and operational excellence.</p>
        
        <p>Maria Thompson, our Customer Service Manager, has been with the company for over eight years and has consistently demonstrated exceptional leadership and problem-solving skills. Under her guidance, our customer satisfaction scores have reached an all-time high of 98%.</p>
        
        <p>Delivery driver Michael Brown has been recognized by multiple customers for his professionalism and reliability. Despite challenging weather conditions and difficult routes, Michael maintains a perfect on-time delivery record and always goes the extra mile to ensure customer satisfaction.</p>
        
        <p>Warehouse supervisor Jennifer Clarke has implemented innovative inventory management processes that have reduced order processing time by 30% while maintaining 99.9% accuracy. Her attention to detail and commitment to continuous improvement have made a significant impact on our operations.</p>
        
        <p>These team members represent the best of EverydayValueJamaica's culture of excellence, teamwork, and customer focus. We're proud to have such dedicated professionals on our team and grateful for their contributions to our success.</p>
      `,
      author: 'HR Team',
      date: '2024-12-28',
      category: 'Employee News',
      image: 'https://images.pexels.com/photos/3184338/pexels-photo-3184338.jpeg?auto=compress&cs=tinysrgb&w=800&h=400&fit=crop',
      tags: ['employees', 'customer service', 'recognition', 'team'],
      featured: false
    },
    {
      id: '5',
      title: 'Technology Upgrade: New Inventory Management System',
      excerpt: 'Discover how our new state-of-the-art inventory management system is improving efficiency and accuracy across all our operations.',
      content: `
        <p>Everyday Value Jamaica has successfully implemented a cutting-edge inventory management system that significantly enhances our operational efficiency and accuracy. This technology upgrade represents a major step forward in our digital transformation journey.</p>
        
        <p>The new system provides real-time visibility into inventory levels across all our facilities, enabling better demand forecasting and more efficient stock management. Advanced analytics help us identify trends and optimize our purchasing decisions.</p>
        
        <p>Key features include automated reorder points, predictive analytics for demand planning, and integrated quality control tracking. The system also provides detailed reporting capabilities that help us identify opportunities for improvement and cost savings.</p>
        
        <p>Our team has undergone comprehensive training on the new system, and early results are extremely promising. We've seen a 25% reduction in stock-outs and a 15% improvement in inventory turnover rates since implementation.</p>
        
        <p>This investment in technology demonstrates our commitment to operational excellence and our dedication to providing the best possible service to our retail partners and their customers.</p>
      `,
      author: 'IT Team',
      date: '2024-12-20',
      category: 'Technology',
      image: 'https://images.pexels.com/photos/4481259/pexels-photo-4481259.jpeg?auto=compress&cs=tinysrgb&w=800&h=400&fit=crop',
      tags: ['technology', 'inventory', 'efficiency', 'digital transformation'],
      featured: false
    }
  ];

  const categories = ['all', 'Company News', 'Partnerships', 'Sustainability', 'Employee News', 'Technology'];

  // Predictive search suggestions (titles + tags)
  const allSuggestions = useMemo(() => {
    const titles = articles.map(a => a.title);
    const tags = Array.from(new Set(articles.flatMap(a => a.tags)));
    return [...titles, ...tags];
  }, [articles]);
  const filteredSuggestions = searchTerm.length > 1
    ? allSuggestions.filter(s => s.toLowerCase().includes(searchTerm.toLowerCase())).slice(0, 6)
    : [];

  const filteredArticles = articles.filter(article => {
    const matchesCategory = selectedCategory === 'all' || article.category === selectedCategory;
    const matchesSearch = article.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         article.excerpt.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const featuredArticles = filteredArticles.filter(article => article.featured);
  const regularArticles = filteredArticles.filter(article => !article.featured);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  const shareArticle = (platform: string, article: NewsArticle) => {
    const url = encodeURIComponent(window.location.href);
    const title = encodeURIComponent(article.title);
    
    let shareUrl = '';
    switch (platform) {
      case 'facebook':
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${url}`;
        break;
      case 'twitter':
        shareUrl = `https://twitter.com/intent/tweet?url=${url}&text=${title}`;
        break;
      case 'linkedin':
        shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${url}`;
        break;
    }
    
    if (shareUrl) {
      window.open(shareUrl, '_blank', 'width=600,height=400');
    }
  };

  // Helper: estimate read time (200wpm)
  function getReadTime(content: string) {
    const text = content.replace(/<[^>]+>/g, '');
    const words = text.split(/\s+/).length;
    return Math.max(1, Math.round(words / 200));
  }

  // Helper: get author avatar (initials)
  function getAvatar(name: string) {
    const initials = name.split(' ').map(n => n[0]).join('').toUpperCase();
    return (
      <div className="w-8 h-8 rounded-full bg-accent-100 text-accent-700 flex items-center justify-center font-bold text-base shadow">
        {initials}
      </div>
    );
  }

  // Update gradient on mount and every minute
  useEffect(() => {
    const interval = setInterval(() => setGradient(getDynamicGradient()), 60000);
    return () => clearInterval(interval);
  }, []);

  // Trending Now slider logic
  const trendingArticles = featuredArticles.slice(0, 3);

  function handleReadAloud(article: NewsArticle) {
    if (!window.speechSynthesis) return;
    window.speechSynthesis.cancel();
    setIsSpeaking(true);
    const utter = new window.SpeechSynthesisUtterance(
      article.title + '. ' + article.excerpt.replace(/<[^>]+>/g, '') + '. ' + article.content.replace(/<[^>]+>/g, '')
    );
    utter.lang = ttsLang === 'en' ? 'en-US' : 'es-ES';
    utter.rate = 1;
    utter.onend = () => setIsSpeaking(false);
    utter.onerror = () => setIsSpeaking(false);
    window.speechSynthesis.speak(utter);
  }
  function stopReadAloud() {
    if (window.speechSynthesis) window.speechSynthesis.cancel();
    setIsSpeaking(false);
  }

  const articlesContainerRef = useRef<HTMLDivElement>(null);
  const [mobileFiltersOpen, setMobileFiltersOpen] = useState(false);
  const [visibleCount, setVisibleCount] = useState(9); // Show 9 articles initially

  // Infinite scroll handler
  useEffect(() => {
    function onScroll() {
      if (!articlesContainerRef.current) return;
      const { scrollTop, scrollHeight, clientHeight } = document.documentElement;
      if (scrollTop + clientHeight >= scrollHeight - 200) {
        setVisibleCount((prev) => Math.min(prev + 6, filteredArticles.length));
      }
    }
    window.addEventListener('scroll', onScroll);
    return () => window.removeEventListener('scroll', onScroll);
  }, [filteredArticles.length]);

  const visibleArticles = filteredArticles.slice(0, visibleCount);

  return (
    <>
      <SEO
        title="News & Updates - EverydayValueJM"
        description="Stay updated with the latest news, announcements, and insights from EverydayValueJM. Read about our company updates, partnerships, and industry developments."
        keywords="news, updates, announcements, EverydayValueJM, Jamaica, distribution, company news, partnerships"
      />

      <div className="pt-20">
        <motion.section
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1 }}
          className="w-full min-h-[260px] flex items-end justify-center relative overflow-hidden"
          style={{ background: gradient, transition: 'background 1s' }}
        >
          {/* Animated background overlay for extra depth */}
          <div className="absolute inset-0 bg-diagonal-pattern opacity-10 z-0" />
          <div className="container mx-auto px-6 sm:px-8 relative z-10">
            <motion.div
              ref={ref}
              initial={{ opacity: 0, y: 40 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8 }}
              className="text-center py-16 sm:py-24"
            >
              <h1 className="text-3xl sm:text-4xl md:text-5xl font-heading font-bold mb-6 sm:mb-8 text-white drop-shadow-lg">
                News & Updates
              </h1>
            </motion.div>
          </div>
          {/* Soft fade divider to next section */}
          <div className="absolute bottom-0 left-0 w-full h-16 bg-gradient-to-b from-transparent to-white z-20 pointer-events-none" />
        </motion.section>

        <section className="bg-white w-full border-b border-neutral-100">
          <div className="container mx-auto px-6 sm:px-8 py-6 flex flex-col md:flex-row md:items-center md:justify-between gap-6">
            {/* Mobile filter toggle */}
            <div className="flex md:hidden justify-between items-center mb-2">
              <button
                onClick={() => setMobileFiltersOpen((v) => !v)}
                className="px-4 py-2 rounded-lg bg-accent-500 text-white font-semibold shadow-md"
              >
                {mobileFiltersOpen ? 'Hide Filters' : 'Show Filters'}
              </button>
            </div>
            {/* Animated Category Filter Chips (collapsible on mobile) */}
            <AnimatePresence>
              {(mobileFiltersOpen || window.innerWidth >= 768) && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.3 }}
                  className="flex flex-wrap gap-3 items-center mb-2"
                >
                  {categories.map((category, i) => (
                    <motion.button
                      key={category}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: 10 }}
                      transition={{ delay: 0.05 * i, duration: 0.3 }}
                      onClick={() => setSelectedCategory(category)}
                      className={`px-4 py-2 rounded-full font-semibold text-sm transition-all border shadow-sm
                        ${selectedCategory === category
                          ? 'bg-accent-500 text-white border-accent-500 shadow-md'
                          : 'bg-neutral-100 text-neutral-700 border-neutral-200 hover:bg-accent-50 hover:text-accent-600'}`}
                    >
                      {category === 'all' ? 'All' : category}
                    </motion.button>
                  ))}
                </motion.div>
              )}
            </AnimatePresence>
            {/* Predictive Search Bar with Suggestions */}
            <div className="relative w-full md:w-96">
              <input
                type="text"
                placeholder="Search news, topics, or tags..."
                value={searchTerm}
                onChange={e => {
                  setSearchTerm(e.target.value);
                  setShowSuggestions(true);
                }}
                onFocus={() => setShowSuggestions(true)}
                onBlur={() => setTimeout(() => setShowSuggestions(false), 150)}
                className="w-full pl-10 pr-4 py-3 border border-neutral-200 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-transparent transition-all text-base shadow-sm"
                aria-label="Search news"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-5 h-5 pointer-events-none" />
              {/* Animated Suggestions Dropdown */}
              <AnimatePresence>
                {showSuggestions && filteredSuggestions.length > 0 && (
                  <motion.ul
                    initial={{ opacity: 0, y: 8 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 8 }}
                    transition={{ duration: 0.2 }}
                    className="absolute left-0 right-0 mt-2 bg-white border border-neutral-200 rounded-lg shadow-lg z-20 overflow-hidden"
                  >
                    {filteredSuggestions.map(s => (
                      <li
                        key={s}
                        className="px-4 py-2 hover:bg-accent-50 cursor-pointer text-sm text-neutral-700"
                        onMouseDown={() => {
                          setSearchTerm(s);
                          setShowSuggestions(false);
                        }}
                      >
                        {s}
                      </li>
                    ))}
                  </motion.ul>
                )}
              </AnimatePresence>
            </div>
            {/* Card/List View Toggle (scaffold) */}
            <div className="flex gap-2 items-center">
              <button
                onClick={() => setViewMode('card')}
                className={`px-3 py-2 rounded-lg text-sm font-semibold transition-all ${viewMode==='card' ? 'bg-accent-500 text-white' : 'bg-neutral-100 text-neutral-700 hover:bg-accent-50 hover:text-accent-600'}`}
              >
                Card View
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`px-3 py-2 rounded-lg text-sm font-semibold transition-all ${viewMode==='list' ? 'bg-accent-500 text-white' : 'bg-neutral-100 text-neutral-700 hover:bg-accent-50 hover:text-accent-600'}`}
              >
                List View
              </button>
            </div>
          </div>
        </section>

        <section className="py-16 sm:py-20 lg:py-24 bg-white">
          <div className="container mx-auto px-6 sm:px-8" ref={articlesContainerRef}>
            <AnimatePresence mode="wait">
              {viewMode === 'card' ? (
                <motion.div
                  key="card"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.5 }}
                  className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
                >
                  {visibleArticles.map((article, index) => (
                    <motion.div
                      key={article.id}
                      layout
                      initial={{ opacity: 0, scale: 0.95 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.95 }}
                      transition={{ duration: 0.4, delay: index * 0.04 }}
                      className="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border border-neutral-100 hover:border-accent-200 cursor-pointer touch-pan-x"
                      onClick={() => setSelectedArticle(article)}
                      tabIndex={0}
                      aria-label={`Open article: ${article.title}`}
                      role="button"
                      onKeyDown={e => { if (e.key === 'Enter' || e.key === ' ') setSelectedArticle(article); }}
                    >
                      <div className="relative group cursor-pointer" onClick={() => setSelectedArticle(article)}>
                        {/* Floating Bookmark Heart */}
                        <motion.button
                          onClick={e => { e.stopPropagation(); setBookmarked(b => ({ ...b, [article.id]: !b[article.id] })); }}
                          whileTap={{ scale: 1.3 }}
                          className="absolute top-4 right-4 z-20 bg-white/80 rounded-full p-2 shadow hover:bg-accent-50 transition-all"
                          aria-label={bookmarked[article.id] ? 'Remove Bookmark' : 'Bookmark'}
                        >
                          <motion.span
                            animate={bookmarked[article.id] ? { scale: [1, 1.5, 1], color: '#fbbf24' } : { scale: 1, color: '#e5e7eb' }}
                            transition={{ type: 'spring', stiffness: 400, damping: 10 }}
                            className="inline-block"
                          >
                            <Heart className={`w-5 h-5 ${bookmarked[article.id] ? 'fill-[#fbbf24] text-[#fbbf24]' : 'text-neutral-300'}`} />
                          </motion.span>
                        </motion.button>
                        {/* Image with hover zoom/floating effect */}
                        <div className="relative h-48 md:h-56 overflow-hidden rounded-t-2xl">
                          <motion.img
                            src={article.image}
                            alt={article.title}
                            className="w-full h-full object-cover group-hover:scale-110 group-hover:-translate-y-1 transition-transform duration-500"
                            whileHover={{ scale: 1.1, y: -4 }}
                            transition={{ type: 'spring', stiffness: 200, damping: 20 }}
                          />
                          {/* Animated Category Label */}
                          <motion.span
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.2, duration: 0.4 }}
                            className="absolute top-4 left-4 bg-gradient-to-r from-accent-500 to-accent-400 text-white px-3 py-1 rounded-full text-xs font-semibold shadow-lg"
                          >
                            {article.category}
                          </motion.span>
                        </div>
                        {/* Card Body */}
                        <div className="p-6 flex flex-col gap-2">
                          <div className="flex items-center gap-3 mb-1">
                            {/* Author Avatar */}
                            {getAvatar(article.author)}
                            <span className="text-sm text-neutral-600 font-medium">{article.author}</span>
                            <span className="text-xs text-neutral-400 ml-2">• {getReadTime(article.content)} min read</span>
                          </div>
                          <h3 className="text-lg font-heading font-bold text-primary-900 mb-1 group-hover:text-accent-600 transition-colors">
                            {article.title}
                          </h3>
                          <p className="text-neutral-600 text-sm leading-relaxed mb-2 line-clamp-3">
                            {article.excerpt}
                          </p>
                          <div className="flex items-center gap-2 flex-wrap mt-1">
                            {article.tags.map(tag => (
                              <motion.span
                                key={tag}
                                whileHover={{ scale: 1.1, background: 'linear-gradient(90deg,#fbbf24,#38bdf8)' }}
                                className="px-2 py-1 bg-accent-50 text-accent-700 rounded text-xs font-medium cursor-pointer transition-all"
                              >
                                {tag}
                              </motion.span>
                            ))}
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </motion.div>
              ) : (
                <motion.div
                  key="list"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.5 }}
                  className="space-y-6"
                >
                  {visibleArticles.map((article, index) => (
                    <motion.div
                      key={article.id}
                      layout
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -20 }}
                      transition={{ duration: 0.4, delay: index * 0.04 }}
                      className="group flex bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-neutral-100 hover:border-accent-200 cursor-pointer touch-pan-x"
                      onClick={() => setSelectedArticle(article)}
                      tabIndex={0}
                      aria-label={`Open article: ${article.title}`}
                      role="button"
                      onKeyDown={e => { if (e.key === 'Enter' || e.key === ' ') setSelectedArticle(article); }}
                    >
                      <div className="w-40 h-40 flex-shrink-0 bg-neutral-100 flex items-center justify-center">
                        <img
                          src={article.image}
                          alt={article.title}
                          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                        />
                      </div>
                      <div className="p-6 flex flex-col gap-2 flex-1">
                        <span className="text-xs font-semibold bg-accent-100 text-accent-700 px-3 py-1 rounded-full w-fit mb-2">
                          {article.category}
                        </span>
                        <h3 className="text-lg font-heading font-bold text-primary-900 mb-1 group-hover:text-accent-600 transition-colors">
                          {article.title}
                        </h3>
                        <p className="text-neutral-600 text-sm leading-relaxed mb-2 line-clamp-2">
                          {article.excerpt}
                        </p>
                        <div className="flex items-center gap-2 flex-wrap">
                          {article.tags.slice(0, 2).map(tag => (
                            <span key={tag} className="px-2 py-1 bg-accent-50 text-accent-700 rounded text-xs font-medium">
                              {tag}
                            </span>
                          ))}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </motion.div>
              )}
            </AnimatePresence>
            {/* Infinite scroll loader */}
            {visibleCount < filteredArticles.length && (
              <div className="flex justify-center py-8">
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="w-10 h-10 rounded-full border-4 border-accent-200 border-t-accent-500 animate-spin"
                  aria-label="Loading more articles..."
                />
              </div>
            )}
            {/* No Results */}
            {filteredArticles.length === 0 && (
              <div className="text-center py-16">
                <div className="w-24 h-24 bg-neutral-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Search className="w-12 h-12 text-neutral-400" />
                </div>
                <h3 className="text-xl font-heading font-bold text-neutral-700 mb-4">
                  No Articles Found
                </h3>
                <p className="text-neutral-600 mb-8">
                  Try adjusting your search criteria or browse all articles.
                </p>
                <button
                  onClick={() => {
                    setSearchTerm('');
                    setSelectedCategory('all');
                  }}
                  className="bg-accent-500 hover:bg-accent-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors"
                >
                  Clear Filters
                </button>
              </div>
            )}
          </div>
        </section>

        {/* Article Detail Modal */}
        <AnimatePresence>
          {selectedArticle && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
              onClick={() => setSelectedArticle(null)}
            >
              <motion.div
                initial={{ opacity: 0, scale: 0.8, y: 50 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.8, y: 50 }}
                className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
                onClick={(e) => e.stopPropagation()}
              >
                {/* Modal Header */}
                <div className="relative">
                  {/* Read Aloud & Language Toggle Controls */}
                  <div className="absolute top-4 left-4 flex gap-2 z-20">
                    <button
                      onClick={() => isSpeaking ? stopReadAloud() : handleReadAloud(selectedArticle)}
                      className={`flex items-center gap-1 px-3 py-2 rounded-lg font-semibold text-sm shadow transition-all
                        ${isSpeaking ? 'bg-accent-500 text-white' : 'bg-white/80 text-accent-700 hover:bg-accent-50'}`}
                      aria-label={isSpeaking ? 'Stop Read Aloud' : 'Read Aloud'}
                    >
                      <Volume2 className="w-4 h-4" />
                      {isSpeaking ? 'Stop' : 'Read Aloud'}
                    </button>
                    <button
                      onClick={() => setTtsLang(ttsLang === 'en' ? 'es' : 'en')}
                      className="flex items-center gap-1 px-3 py-2 rounded-lg font-semibold text-sm shadow bg-white/80 text-accent-700 hover:bg-accent-50"
                      aria-label="Toggle Language"
                    >
                      <Globe className="w-4 h-4" />
                      {ttsLang === 'en' ? 'English' : 'Español'}
                    </button>
                  </div>
                  <img
                    src={selectedArticle.image}
                    alt={selectedArticle.title}
                    className="w-full h-64 object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                  <button
                    onClick={() => setSelectedArticle(null)}
                    className="absolute top-4 right-4 p-2 bg-white/20 hover:bg-white/30 rounded-lg transition-colors"
                  >
                    <X className="w-6 h-6 text-white" />
                  </button>
                  <div className="absolute bottom-4 left-4 right-4 text-white">
                    <div className="flex items-center space-x-4 text-sm mb-2">
                      <span className="flex items-center space-x-1">
                        <Calendar className="w-4 h-4" />
                        <span>{formatDate(selectedArticle.date)}</span>
                      </span>
                      <span className="flex items-center space-x-1">
                        <User className="w-4 h-4" />
                        <span>{selectedArticle.author}</span>
                      </span>
                      <span className="bg-white/20 backdrop-blur-sm px-2 py-1 rounded text-xs">
                        {selectedArticle.category}
                      </span>
                    </div>
                    <h2 className="text-2xl font-heading font-bold">{selectedArticle.title}</h2>
                  </div>
                </div>

                {/* Modal Content */}
                <div className="p-6 sm:p-8 max-h-96 overflow-y-auto">
                  <div 
                    className="prose prose-neutral max-w-none"
                    dangerouslySetInnerHTML={{ __html: selectedArticle.content }}
                  />
                  
                  {/* Tags */}
                  <div className="flex flex-wrap gap-2 mt-8 pt-6 border-t border-neutral-200">
                    {selectedArticle.tags.map((tag) => (
                      <span
                        key={tag}
                        className="px-3 py-1 bg-accent-50 text-accent-700 rounded-full text-sm font-medium flex items-center space-x-1"
                      >
                        <Tag className="w-3 h-3" />
                        <span>{tag}</span>
                      </span>
                    ))}
                  </div>
                </div>

                {/* Modal Footer */}
                <div className="border-t border-neutral-200 p-6 sm:p-8">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Share2 className="w-4 h-4 text-neutral-600" />
                      <span className="text-neutral-600 text-sm font-medium">Share this article:</span>
                    </div>
                    <div className="flex space-x-3">
                      <button
                        onClick={() => shareArticle('facebook', selectedArticle)}
                        className="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                      >
                        <Facebook className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => shareArticle('twitter', selectedArticle)}
                        className="p-2 bg-blue-400 text-white rounded-lg hover:bg-blue-500 transition-colors"
                      >
                        <Twitter className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => shareArticle('linkedin', selectedArticle)}
                        className="p-2 bg-blue-700 text-white rounded-lg hover:bg-blue-800 transition-colors"
                      >
                        <Linkedin className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {showTrending && (
        <>
          {/* Sticky Trending Now Slider (desktop right, mobile bottom) */}
          <div className="fixed z-40 right-8 top-1/4 hidden xl:block w-80">
            <motion.div
              initial={{ opacity: 0, x: 40 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              className="bg-white/95 rounded-2xl shadow-2xl border border-accent-100 p-4 flex flex-col gap-4 sticky top-24 relative"
            >
              {/* X Button */}
              <button
                onClick={() => setShowTrending(false)}
                className="absolute top-2 right-2 text-accent-500 hover:text-accent-700 text-xl font-bold bg-white/80 rounded-full w-8 h-8 flex items-center justify-center shadow transition-all"
                aria-label="Close Trending Now"
              >
                ×
              </button>
              <div className="font-bold text-primary-900 text-lg mb-2 flex items-center gap-2">
                <span className="bg-gradient-to-r from-accent-500 to-accent-400 text-white px-3 py-1 rounded-full text-xs font-semibold">Trending Now</span>
              </div>
              <div className="flex gap-4 overflow-x-auto pb-2 scrollbar-thin scrollbar-thumb-accent-200">
                {trendingArticles.map(article => (
                  <motion.div
                    key={article.id}
                    whileHover={{ scale: 1.05 }}
                    className="min-w-[180px] max-w-[180px] bg-accent-50 rounded-xl shadow hover:shadow-lg transition-all cursor-pointer flex-shrink-0"
                    onClick={() => setSelectedArticle(article)}
                  >
                    <div className="h-24 w-full rounded-t-xl overflow-hidden">
                      <img src={article.image} alt={article.title} className="w-full h-full object-cover" />
                    </div>
                    <div className="p-3">
                      <div className="text-xs font-semibold text-accent-700 mb-1 truncate">{article.category}</div>
                      <div className="font-bold text-sm text-primary-900 truncate mb-1">{article.title}</div>
                      <div className="text-xs text-neutral-500">{getReadTime(article.content)} min read</div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </div>
          {/* Mobile Trending Now Slider (bottom) */}
          <div className="fixed bottom-0 left-0 right-0 z-40 xl:hidden bg-white/95 border-t border-accent-100 shadow-2xl px-2 py-2 flex gap-3 overflow-x-auto scrollbar-thin scrollbar-thumb-accent-200 relative">
            {/* X Button */}
            <button
              onClick={() => setShowTrending(false)}
              className="absolute top-2 right-2 text-accent-500 hover:text-accent-700 text-xl font-bold bg-white/80 rounded-full w-8 h-8 flex items-center justify-center shadow transition-all"
              aria-label="Close Trending Now"
            >
              ×
            </button>
            <div className="flex items-center gap-2 pl-2">
              <span className="bg-gradient-to-r from-accent-500 to-accent-400 text-white px-3 py-1 rounded-full text-xs font-semibold">Trending Now</span>
            </div>
            {trendingArticles.map(article => (
              <motion.div
                key={article.id}
                whileHover={{ scale: 1.05 }}
                className="min-w-[140px] max-w-[140px] bg-accent-50 rounded-xl shadow hover:shadow-lg transition-all cursor-pointer flex-shrink-0"
                onClick={() => setSelectedArticle(article)}
              >
                <div className="h-14 w-full rounded-t-xl overflow-hidden">
                  <img src={article.image} alt={article.title} className="w-full h-full object-cover" />
                </div>
                <div className="p-2">
                  <div className="text-xs font-semibold text-accent-700 truncate">{article.category}</div>
                  <div className="font-bold text-xs text-primary-900 truncate">{article.title}</div>
                </div>
              </motion.div>
            ))}
          </div>
        </>
      )}
    </>
  );
};

export default News;