import React from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { Factory, Truck, Package, Users, Clock, Shield, Award, Star } from 'lucide-react';
import { Link } from 'react-router-dom';
import SEO from '../components/common/SEO';

const WhatWeDo: React.FC = () => {
  const [heroRef, heroInView] = useInView({ triggerOnce: true, threshold: 0.1 });

  const services = [
    {
      icon: Factory,
      title: 'Manufacturing Excellence',
      description: 'State-of-the-art manufacturing facilities with cutting-edge technology for contract manufacturing, private labeling, and co-packing services.',
      features: ['Contract Manufacturing', 'Private Label Development', 'Co-Packing Services', 'Quality Control Systems']
    },
    {
      icon: Truck,
      title: 'Distribution Network',
      description: 'Comprehensive islandwide distribution with real-time tracking, flexible scheduling, and temperature-controlled transport solutions.',
      features: ['Islandwide Coverage', 'Real-time Tracking', 'Cold Chain Logistics', 'Express Delivery']
    }
  ];



  return (
    <>
      <SEO
        title="What We Do - Everyday Value Jamaica"
        description="Discover Everyday Value Jamaica's comprehensive distribution services including logistics, warehousing, sales support, and customer care across Jamaica."
        keywords="distribution services, logistics Jamaica, warehousing, sales support, customer care, islandwide delivery"
      />

      {/* Hero Section */}
      <section className="py-16 sm:py-20 lg:py-24 bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700 text-white">
        <div className="container mx-auto px-6 sm:px-8">
          <motion.div
            ref={heroRef}
            initial={{ opacity: 0, y: 50 }}
            animate={heroInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2, duration: 0.6 }}
              className="inline-flex items-center space-x-2 bg-accent-500/20 backdrop-blur-sm border border-accent-400/30 rounded-full px-6 py-3 mb-8"
            >
              <Star className="w-5 h-5 text-accent-400" />
              <span className="text-accent-400 font-medium">Manufacturing & Distribution Excellence</span>
            </motion.div>

            <motion.h1
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.8 }}
              className="text-4xl sm:text-5xl md:text-6xl font-heading font-bold mb-8 leading-tight"
            >
              What We Do
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.8 }}
              className="text-xl sm:text-2xl text-neutral-200 max-w-4xl mx-auto leading-relaxed mb-12"
            >
              We seamlessly integrate advanced manufacturing with comprehensive distribution,
              delivering end-to-end solutions that transform ideas into market success across Jamaica.
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8, duration: 0.8 }}
              className="flex flex-col sm:flex-row gap-4 justify-center mb-16"
            >
              <Link
                to="/manufacturing"
                className="inline-flex items-center space-x-3 bg-accent-500 hover:bg-accent-600 text-white px-8 py-4 rounded-full font-semibold text-lg transition-all duration-300"
              >
                <Factory className="w-6 h-6" />
                <span>Manufacturing</span>
              </Link>
              <Link
                to="/distribution"
                className="inline-flex items-center space-x-3 border-2 border-white text-white hover:bg-white hover:text-primary-900 px-8 py-4 rounded-full font-semibold text-lg transition-all duration-300"
              >
                <Truck className="w-6 h-6" />
                <span>Distribution</span>
              </Link>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16 sm:py-20 lg:py-24 bg-white">
        <div className="container mx-auto px-6 sm:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl md:text-5xl font-heading font-bold text-primary-900 mb-6">
              Our Services
            </h2>
            <p className="text-lg sm:text-xl text-neutral-600 max-w-4xl mx-auto leading-relaxed mb-8">
              Experience our integrated approach where cutting-edge manufacturing meets comprehensive distribution excellence.
            </p>

            {/* Service Tabs */}
            <div className="flex justify-center mb-12">
              <div className="bg-neutral-100 rounded-full p-2 flex">
                <Link
                  to="/manufacturing"
                  className="flex items-center space-x-2 px-6 py-3 rounded-full font-semibold transition-all duration-300 bg-accent-500 text-white hover:bg-accent-600"
                >
                  <Factory className="w-5 h-5" />
                  <span>Manufacturing</span>
                </Link>
                <Link
                  to="/distribution"
                  className="flex items-center space-x-2 px-6 py-3 rounded-full font-semibold transition-all duration-300 text-neutral-700 hover:bg-white hover:text-accent-500"
                >
                  <Truck className="w-5 h-5" />
                  <span>Distribution</span>
                </Link>
              </div>
            </div>
          </div>

          <div className="grid lg:grid-cols-2 gap-8">
            {services.map((service, index) => (
              <motion.div
                key={service.title}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-neutral-50 rounded-3xl p-8 hover:shadow-lg transition-shadow group"
              >
                <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-accent-500 to-accent-600 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                  <service.icon className="w-8 h-8 text-white" />
                </div>

                <h3 className="text-2xl font-heading font-bold text-primary-900 mb-4">
                  {service.title}
                </h3>

                <p className="text-neutral-600 leading-relaxed mb-6">
                  {service.description}
                </p>

                <ul className="space-y-3 mb-6">
                  {service.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center space-x-3">
                      <Package className="w-5 h-5 text-accent-500 flex-shrink-0" />
                      <span className="text-neutral-700 font-medium">{feature}</span>
                    </li>
                  ))}
                </ul>

                <Link
                  to={service.title === 'Manufacturing Excellence' ? '/manufacturing' : '/distribution'}
                  className="inline-flex items-center space-x-2 text-accent-500 hover:text-accent-600 font-semibold transition-colors"
                >
                  <span>Learn More</span>
                  <Package className="w-4 h-4" />
                </Link>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

    </>
  );
};

export default WhatWeDo;
