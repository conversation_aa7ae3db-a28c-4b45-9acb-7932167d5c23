import React, { useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Search, X, Package, Tag, Building, MessageSquare } from 'lucide-react';
import useSearch from '../../hooks/useSearch';

const GlobalSearchBar: React.FC = () => {
  const {
    searchTerm,
    setSearchTerm,
    isSearchOpen,
    setIsSearchOpen,
    suggestions
  } = useSearch();

  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const openWhatsApp = () => {
    const whatsappNumber = "18764331359";
    const message = encodeURIComponent(`Hi! I was searching for "${searchTerm}" on your website. Can you help me find what I'm looking for?`);
    const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${message}`;
    window.open(whatsappUrl, '_blank');
  };

  // Handle click outside to close search
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsSearchOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [setIsSearchOpen]);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + K to open search
      if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault();
        setIsSearchOpen(true);
        inputRef.current?.focus();
      }
      
      // Escape to close search
      if (event.key === 'Escape') {
        setIsSearchOpen(false);
        setSearchTerm('');
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [setIsSearchOpen, setSearchTerm]);

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'product':
        return <Package className="w-4 h-4" />;
      case 'brand':
        return <Building className="w-4 h-4" />;
      case 'category':
        return <Tag className="w-4 h-4" />;
      default:
        return <Search className="w-4 h-4" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'product':
        return 'text-blue-600 bg-blue-50';
      case 'brand':
        return 'text-green-600 bg-green-50';
      case 'category':
        return 'text-purple-600 bg-purple-50';
      default:
        return 'text-neutral-600 bg-neutral-50';
    }
  };

  return (
    <div ref={searchRef} className="relative w-full max-w-2xl mx-auto">
      {/* Search Input */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-neutral-400" />
        </div>
        <input
          ref={inputRef}
          type="text"
          placeholder="Search products, brands, or categories... (Ctrl+K)"
          value={searchTerm}
          onChange={(e) => {
            setSearchTerm(e.target.value);
            setIsSearchOpen(true);
          }}
          onFocus={() => setIsSearchOpen(true)}
          className="block w-full pl-10 pr-12 py-3 border border-neutral-200 rounded-xl bg-white/90 backdrop-blur-sm focus:ring-2 focus:ring-accent-500 focus:border-transparent transition-all duration-200 text-sm sm:text-base placeholder-neutral-500"
        />
        {searchTerm && (
          <button
            onClick={() => {
              setSearchTerm('');
              setIsSearchOpen(false);
            }}
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
          >
            <X className="h-5 w-5 text-neutral-400 hover:text-neutral-600 transition-colors" />
          </button>
        )}
      </div>

      {/* Search Results Dropdown */}
      <AnimatePresence>
        {isSearchOpen && (searchTerm.length >= 2 || suggestions.length > 0) && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute top-full left-0 right-0 mt-2 bg-white rounded-xl shadow-2xl border border-neutral-200 overflow-hidden z-50 max-h-96 overflow-y-auto"
          >
            {suggestions.length > 0 ? (
              <div className="py-2">
                {suggestions.map((suggestion, index) => (
                  <motion.button
                    key={suggestion.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.2, delay: index * 0.05 }}
                    onClick={suggestion.action}
                    className="w-full px-4 py-3 text-left hover:bg-neutral-50 transition-colors duration-150 flex items-center space-x-3 group"
                  >
                    {/* Type Icon */}
                    <div className={`p-2 rounded-lg ${getTypeColor(suggestion.type)}`}>
                      {getTypeIcon(suggestion.type)}
                    </div>

                    {/* Image (for products and brands) */}
                    {suggestion.image && (
                      <div className="w-10 h-10 rounded-lg overflow-hidden bg-neutral-100 flex-shrink-0">
                        <img
                          src={suggestion.image}
                          alt={suggestion.text}
                          className="w-full h-full object-contain"
                        />
                      </div>
                    )}

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-neutral-900 group-hover:text-accent-600 transition-colors truncate">
                        {suggestion.text}
                      </div>
                      {suggestion.brand && (
                        <div className="text-sm text-neutral-500 truncate">
                          {suggestion.brand}
                        </div>
                      )}
                      {suggestion.category && (
                        <div className="text-xs text-neutral-400 truncate">
                          {suggestion.category}
                        </div>
                      )}
                    </div>

                    {/* Type Badge */}
                    <div className="text-xs text-neutral-400 capitalize bg-neutral-100 px-2 py-1 rounded-full">
                      {suggestion.type}
                    </div>
                  </motion.button>
                ))}
                
                {/* WhatsApp Help Option */}
                {searchTerm.length >= 2 && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: suggestions.length * 0.05 + 0.1 }}
                    className="border-t border-neutral-100 mt-2 pt-2"
                  >
                    <button
                      onClick={openWhatsApp}
                      className="w-full px-4 py-3 text-left hover:bg-green-50 transition-colors duration-150 flex items-center space-x-3 group"
                    >
                      <div className="p-2 rounded-lg bg-green-50 text-green-600">
                        <MessageSquare className="w-4 h-4" />
                      </div>
                      <div className="flex-1">
                        <div className="font-medium text-green-700 group-hover:text-green-800 transition-colors">
                          Can't find what you're looking for?
                        </div>
                        <div className="text-sm text-green-600">
                          Chat with us on WhatsApp for personalized help
                        </div>
                      </div>
                      <div className="text-xs text-green-500 bg-green-100 px-2 py-1 rounded-full">
                        WhatsApp
                      </div>
                    </button>
                  </motion.div>
                )}
              </div>
            ) : searchTerm.length >= 2 ? (
              <div className="py-8 text-center text-neutral-500">
                <Search className="w-8 h-8 mx-auto mb-2 text-neutral-300" />
                <p>No results found for "{searchTerm}"</p>
                <p className="text-sm mt-1">Try searching for products, brands, or categories</p>
                
                {/* WhatsApp Help for No Results */}
                <div className="mt-4">
                  <button
                    onClick={openWhatsApp}
                    className="inline-flex items-center space-x-2 text-green-600 hover:text-green-700 font-medium transition-colors"
                  >
                    <MessageSquare className="w-4 h-4" />
                    <span>Ask us on WhatsApp</span>
                  </button>
                </div>
              </div>
            ) : (
              <div className="py-8 text-center text-neutral-400">
                <Search className="w-8 h-8 mx-auto mb-2 text-neutral-300" />
                <p>Start typing to search...</p>
                <p className="text-sm mt-1">Search products, brands, or categories</p>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default GlobalSearchBar;