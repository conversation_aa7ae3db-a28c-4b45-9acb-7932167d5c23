import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { Star, ArrowRight, X } from 'lucide-react';
import { useLocation } from 'react-router-dom';
import SEO from '../components/common/SEO';
import { brands, products } from '../data/brands';

// Product descriptions for the modal
const getProductDescription = (productName: string): string => {
  const descriptions: { [key: string]: string } = {
    'ADA Gold 2L': 'Premium liquid laundry detergent that delivers exceptional cleaning power while being gentle on fabrics. Perfect for everyday washing needs.',
    'ADA Gold 1L': 'Concentrated liquid laundry detergent in a convenient 1L size. Provides excellent stain removal and fabric care.',
    'ADA Fresh 2L': 'Fresh-scented liquid detergent that leaves clothes smelling clean and feeling soft. Ideal for all fabric types.',
    'ADA Colour Angel 2L': 'Specially formulated to protect and preserve colored fabrics while providing thorough cleaning. Prevents fading and color bleeding.',
    'ADA Colour Angel 1L': 'Color-protecting liquid detergent in a convenient 1L size. Keeps your colored clothes vibrant wash after wash.',
    'ADA Lavender Softener': 'Luxurious fabric softener with a calming lavender scent. Makes clothes feel incredibly soft and smell amazing.',
    'ADA Mild Care': 'Gentle fabric care solution perfect for delicate items and sensitive skin. Provides effective cleaning without harsh chemicals.',
    'ADA Powder Gold': 'High-performance laundry powder that tackles tough stains and odors. Suitable for both hand and machine washing.',
    'ADA Powder Lavender': 'Lavender-scented laundry powder that combines powerful cleaning with a delightful fragrance.',
    'ADA Powder Lemon Fresh': 'Citrus-fresh laundry powder that leaves clothes clean and smelling like fresh lemons.',
    'CHIPPIES Cheese Kurls': 'Crunchy, cheesy snacks that are perfect for any time of day. Made with real cheese for authentic flavor.',
    'CHIPPIES Banana Chips': 'Crispy banana chips made from premium bananas. A healthy and delicious snack option.',
    'CHIPPIES Breadfruit Chips': 'Traditional Caribbean breadfruit chips with a satisfying crunch and authentic island flavor.',
    'CHIPPIES Onion Bits': 'Savory onion-flavored snacks with a perfect crunch. Great for parties or everyday snacking.',
    'CHIPPIES Seasoned Popcorn': 'Perfectly seasoned popcorn with a blend of spices. Light, airy, and full of flavor.',
    'NICIES Lime Juice 750ml': 'Pure, natural lime juice perfect for cooking, cocktails, and refreshing drinks. No artificial additives.',
    'NICIES Lime Juice 475ml': 'Convenient size lime juice for everyday use. Fresh, tangy flavor in a handy bottle.',
    'NICIES Grape': 'Refreshing grape-flavored beverage made with natural grape extracts. Perfect for any occasion.',
    'NICIES Orange': 'Zesty orange drink bursting with citrus flavor. Made with real orange juice for authentic taste.',
    'NICIES Pineapple': 'Tropical pineapple beverage that brings the taste of the islands to your glass.',
    'NICIES Lemonade': 'Classic lemonade with the perfect balance of sweet and tart. Refreshing and thirst-quenching.',
    'St. Bess Peanut Brittle': 'Traditional peanut brittle made with premium peanuts and the perfect amount of sweetness.',
    'St. Bess Pimento': 'Authentic Caribbean pimento (allspice) for adding traditional island flavor to your cooking.',
    'St. Bess Nutmeg': 'Premium ground nutmeg perfect for baking, cooking, and traditional Caribbean dishes.',
    'St. Bess Scallion': 'Dried scallions that add authentic Caribbean flavor to soups, stews, and rice dishes.',
    'St. Bess Thyme': 'Aromatic thyme perfect for seasoning meats, vegetables, and traditional Caribbean cuisine.',
    'Apple Peach Rings': 'Sweet and chewy gummy rings with delicious apple and peach flavors.',
    'Gummy Worms': 'Fun and fruity gummy worms that are perfect for kids and adults alike.',
    'Jolly Ranchers Hard Candy': 'Long-lasting hard candy with intense fruit flavors that pack a punch.',
    'Soft Mint': 'Smooth and creamy mint candies that provide a refreshing taste experience.',
    'Sour Fruit Slice': 'Tangy and sweet fruit-shaped gummies with a perfect sour coating.',
    'Sour Gummy Bears': 'Classic gummy bears with a sour twist that delivers the perfect balance of sweet and tart.',
    'Starlight Mints': 'Classic peppermint hard candies with a refreshing mint flavor.',
    'Watermelon Raspberry Rings': 'Delicious gummy rings combining the flavors of watermelon and raspberry.'
  };

  return descriptions[productName] || 'A premium product from our carefully curated selection of quality brands.';
};

const Brands: React.FC = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const location = useLocation();
  const [selectedBrand, setSelectedBrand] = useState<string | null>(null);
  const [selectedProduct, setSelectedProduct] = useState<any | null>(null);
  const [zoomedImage, setZoomedImage] = useState<string | null>(null);

  // Handle URL parameters for deep linking
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const brandParam = params.get('brand');

    if (brandParam) {
      setSelectedBrand(brandParam);
    }
  }, [location.search]);

  const filteredBrands = brands;

  const brandProducts = selectedBrand ? products.filter(product => product.brand === selectedBrand) : [];

  return (
    <>
      <SEO
        title="Premium Brands - EverydayValueJM"
        description="Discover the trusted brands that make EverydayValueJM Jamaica's preferred distribution partner. Browse our extensive portfolio of quality brands across multiple categories."
        keywords="brands, distribution, Jamaica, wholesale, retail, premium brands, EverydayValueJM"
      />

      <div className="pt-0"> {/* Removed pt-20 since App.tsx now handles it */}
        <section className="py-16 sm:py-20 lg:py-24 bg-gradient-to-br from-neutral-50 to-white relative overflow-hidden">
          <div className="absolute inset-0 bg-diagonal-pattern opacity-5"></div>
          
          <div className="container mx-auto px-6 sm:px-8 relative z-10">
            <motion.div
              ref={ref}
              initial={{ opacity: 0, y: 50 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8 }}
              className="text-center mb-16 sm:mb-20"
            >
              <h1 className="text-3xl sm:text-4xl md:text-5xl font-heading font-bold text-primary-900 mb-6 sm:mb-8">
                Featured Brands
              </h1>
              <p className="text-lg sm:text-xl text-neutral-600 max-w-4xl mx-auto leading-relaxed px-4">
                From household essentials to specialty products, we proudly represent quality brands across diverse categories.
                Explore the trusted partners that have made EverydayValueJamaica the island's leading distribution choice.
              </p>
            </motion.div>

            {/* Group Image Placeholder Section */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="mt-12 mb-16"
            >
              <div className="bg-gradient-to-r from-amber-50 to-yellow-50 rounded-2xl p-8 sm:p-12 border border-amber-200">
                <div className="text-center">
                  <div className="w-full h-64 sm:h-80 lg:h-96 bg-amber-100 rounded-xl border-2 border-dashed border-amber-300 flex items-center justify-center">
                    <div className="text-amber-600">
                      <div className="text-4xl sm:text-6xl mb-4">📸</div>
                      <h3 className="text-xl sm:text-2xl font-heading font-bold mb-2">Group Image Placeholder</h3>
                      <p className="text-sm sm:text-base opacity-75">Space reserved for brand group images</p>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            <div>
              {/* Main Content */}
              <div>
                {/* Product Grid - 4 per row */}
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={inView ? { opacity: 1 } : {}}
                  transition={{ duration: 0.8, delay: 0.2 }}
                  className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 sm:gap-8"
                >
                  <AnimatePresence>
                    {(() => {
                      // Filter to show only one product per brand
                      const uniqueProducts = products.reduce((acc: any[], product) => {
                        const existingBrand = acc.find(p => p.brand === product.brand);
                        if (!existingBrand) {
                          acc.push(product);
                        }
                        return acc;
                      }, []);

                      return uniqueProducts.map((product, index) => (
                      <motion.div
                        key={product.id}
                        layout
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.8 }}
                        transition={{ duration: 0.5, delay: index * 0.1 }}
                        className="group cursor-pointer"
                        onClick={() => setSelectedProduct(product)}
                      >
                        <div className="bg-gradient-to-b from-amber-100 to-amber-400 rounded-xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden aspect-square relative group-hover:scale-105">
                          <div className="relative p-2 sm:p-3 h-3/4 flex items-center justify-center">
                            <img
                              src={product.image}
                              alt={product.name}
                              className="max-w-full max-h-full object-contain group-hover:scale-105 transition-transform duration-300 drop-shadow-lg scale-125"
                            />
                          </div>
                          <div className="absolute bottom-0 left-0 right-0 h-1/4 bg-gradient-to-t from-amber-600/80 via-amber-500/60 to-amber-400/40 flex items-center justify-center p-2">
                            <h3 className="text-white font-heading font-bold text-xs sm:text-sm text-center leading-tight drop-shadow-lg">
                              {product.name}
                            </h3>
                          </div>
                          <div className="absolute inset-0 bg-gradient-to-t from-amber-600/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                        </div>
                      </motion.div>
                    ));
                    })()}
                  </AnimatePresence>
                </motion.div>
              </div>
            </div>

            {/* Brand Detail Modal */}
            <AnimatePresence>
              {selectedBrand && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
                  onClick={() => setSelectedBrand(null)}
                >
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8, y: 50 }}
                    animate={{ opacity: 1, scale: 1, y: 0 }}
                    exit={{ opacity: 0, scale: 0.8, y: 50 }}
                    className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
                    onClick={(e) => e.stopPropagation()}
                  >
                    {/* Modal Header */}
                    <div className="bg-gradient-to-r from-primary-900 to-primary-700 p-6 sm:p-8 text-white">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div className="w-12 h-12 sm:w-16 sm:h-16 bg-white rounded-xl p-2 flex items-center justify-center">
                            <img
                              src={brands.find(b => b.name === selectedBrand)?.logo}
                              alt={selectedBrand}
                              className="max-w-full max-h-full object-contain"
                            />
                          </div>
                          <div>
                            <h3 className="text-xl sm:text-2xl font-heading font-bold">{selectedBrand}</h3>
                            <p className="text-neutral-200 text-sm sm:text-base">
                              {brands.find(b => b.name === selectedBrand)?.description}
                            </p>
                          </div>
                        </div>
                        <button
                          onClick={() => setSelectedBrand(null)}
                          className="p-2 hover:bg-white/20 rounded-lg transition-colors"
                        >
                          <X className="w-6 h-6" />
                        </button>
                      </div>
                    </div>

                    {/* Modal Content */}
                    <div className="p-6 sm:p-8 max-h-96 overflow-y-auto">
                      <h4 className="text-lg sm:text-xl font-heading font-bold text-primary-900 mb-6">
                        Products from {selectedBrand}
                      </h4>
                      {brandProducts.length > 0 ? (
                        <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                          {brandProducts.map((product) => (
                            <div key={product.id} className="bg-neutral-50 rounded-xl p-4 hover:bg-neutral-100 transition-colors">
                              <div className="bg-white rounded-lg overflow-hidden mb-3 h-32 flex items-center justify-center cursor-pointer relative group" onClick={() => setZoomedImage(product.image)}>
                                <img
                                  src={product.image}
                                  alt={product.name}
                                  className="max-w-full max-h-full object-contain group-hover:scale-105 transition-transform duration-300"
                                />
                                <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M15 10l4.553-4.553a1.414 1.414 0 00-2-2L13 8m-2 8l-4.553 4.553a1.414 1.414 0 002 2L11 16" /></svg>
                                </div>
                              </div>
                              <h5 className="font-semibold text-primary-900 mb-2 text-sm sm:text-base">{product.name}</h5>
                              <p className="text-sm text-neutral-600 mb-3 line-clamp-2">{product.description}</p>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-8">
                          <p className="text-neutral-600">No products available for this brand yet.</p>
                        </div>
                      )}
                    </div>
                  </motion.div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Product Detail Modal */}
            <AnimatePresence>
              {selectedProduct && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
                  onClick={() => setSelectedProduct(null)}
                >
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8, y: 50 }}
                    animate={{ opacity: 1, scale: 1, y: 0 }}
                    exit={{ opacity: 0, scale: 0.8, y: 50 }}
                    className="bg-gradient-to-b from-amber-100 to-amber-400 rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden relative"
                    onClick={(e) => e.stopPropagation()}
                  >
                    {/* Animated Background Elements */}
                    <motion.div
                      animate={{
                        scale: [1, 1.2, 1],
                        opacity: [0.1, 0.3, 0.1],
                        rotate: [0, 180, 360]
                      }}
                      transition={{
                        duration: 20,
                        repeat: Infinity,
                        ease: "linear"
                      }}
                      className="absolute top-10 right-10 w-32 h-32 bg-white/20 rounded-full blur-xl"
                    />
                    <motion.div
                      animate={{
                        scale: [1, 1.5, 1],
                        opacity: [0.05, 0.2, 0.05],
                        rotate: [360, 180, 0]
                      }}
                      transition={{
                        duration: 15,
                        repeat: Infinity,
                        ease: "linear"
                      }}
                      className="absolute bottom-10 left-10 w-24 h-24 bg-white/15 rounded-full blur-2xl"
                    />
                    <motion.div
                      animate={{
                        scale: [1, 1.3, 1],
                        opacity: [0.08, 0.25, 0.08],
                        x: [0, 20, 0],
                        y: [0, -10, 0]
                      }}
                      transition={{
                        duration: 12,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                      className="absolute top-1/2 left-1/4 w-20 h-20 bg-white/10 rounded-full blur-lg"
                    />
                    {/* Close Button */}
                    <button
                      onClick={() => setSelectedProduct(null)}
                      className="absolute top-4 right-4 z-10 p-2 bg-amber-800/20 hover:bg-amber-800/40 rounded-full transition-colors backdrop-blur-sm"
                    >
                      <X className="w-6 h-6 text-amber-800" />
                    </button>



                    {/* Product Content */}
                    <div className="px-6 sm:px-8 pb-8">
                      {/* Animated Product Image */}
                      <div className="flex justify-center mb-6 relative">
                        <motion.div
                          animate={{
                            rotateY: [0, 15, -15, 0],
                            rotateX: [0, 5, -5, 0],
                            scale: [1, 1.15, 1],
                            z: [0, 50, 0]
                          }}
                          transition={{
                            duration: 6,
                            repeat: Infinity,
                            ease: "easeInOut"
                          }}
                          className="relative"
                          style={{
                            transformStyle: 'preserve-3d',
                            perspective: '1000px'
                          }}
                        >
                          {/* 3D Shadow Effect */}
                          <motion.div
                            animate={{
                              scale: [0.9, 1.1, 0.9],
                              opacity: [0.3, 0.6, 0.3]
                            }}
                            transition={{
                              duration: 6,
                              repeat: Infinity,
                              ease: "easeInOut"
                            }}
                            className="absolute inset-0 bg-black/30 rounded-full blur-xl transform translate-y-8 scale-75"
                          />

                          {/* Product Image - No Box */}
                          <motion.img
                            src={selectedProduct.image}
                            alt={selectedProduct.name}
                            className="w-56 h-56 sm:w-72 sm:h-72 object-contain relative z-10"
                            style={{
                              filter: 'drop-shadow(0 25px 50px rgba(0,0,0,0.6)) drop-shadow(0 10px 20px rgba(0,0,0,0.4)) contrast(1.1) saturate(1.1) brightness(1.05)',
                              transform: 'translateZ(30px)'
                            }}
                            animate={{
                              filter: [
                                'drop-shadow(0 25px 50px rgba(0,0,0,0.6)) drop-shadow(0 10px 20px rgba(0,0,0,0.4)) contrast(1.1) saturate(1.1) brightness(1.05)',
                                'drop-shadow(0 35px 70px rgba(0,0,0,0.7)) drop-shadow(0 15px 30px rgba(0,0,0,0.5)) contrast(1.15) saturate(1.15) brightness(1.1)',
                                'drop-shadow(0 25px 50px rgba(0,0,0,0.6)) drop-shadow(0 10px 20px rgba(0,0,0,0.4)) contrast(1.1) saturate(1.1) brightness(1.05)'
                              ]
                            }}
                            transition={{
                              duration: 6,
                              repeat: Infinity,
                              ease: "easeInOut"
                            }}
                          />
                        </motion.div>
                      </div>

                      {/* Product Name */}
                      <div className="text-center">
                        <h3 className="text-2xl sm:text-3xl font-heading font-bold text-amber-800 mb-4">
                          {selectedProduct.name}
                        </h3>

                        {/* Product Description */}
                        <div className="bg-white/50 rounded-xl p-4 sm:p-6 backdrop-blur-sm">
                          <p className="text-amber-800 text-sm sm:text-base leading-relaxed">
                            {getProductDescription(selectedProduct.name)}
                          </p>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </section>
      </div>

      {/* Image Zoom Modal */}
      <AnimatePresence>
        {zoomedImage && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/80 backdrop-blur-sm z-[100] flex items-center justify-center p-4"
            onClick={() => setZoomedImage(null)}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.5 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.5 }}
              className="relative max-w-4xl max-h-[90vh] w-full h-full flex items-center justify-center"
              onClick={(e) => e.stopPropagation()}
            >
              <img
                src={zoomedImage}
                alt="Product Image"
                className="max-w-full max-h-full object-contain rounded-lg shadow-2xl"
              />
              <button
                onClick={() => setZoomedImage(null)}
                className="absolute top-4 right-4 p-3 bg-white/20 hover:bg-white/30 rounded-full transition-colors backdrop-blur-sm"
                aria-label="Close zoomed image"
              >
                <X className="w-6 h-6 text-white" />
              </button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default Brands;