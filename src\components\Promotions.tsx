import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { ChevronLeft, ChevronRight, Clock, Star, ArrowRight, Zap } from 'lucide-react';

const Promotions: React.FC = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [currentSlide, setCurrentSlide] = useState(0);

  const promotions = [
    {
      id: 1,
      title: 'Summer Beverage Blast',
      description: 'Get 20% off all ADA beverages this summer season',
      discount: '20% OFF',
      products: ['ADA Cola Classic', 'ADA Orange Fizz', 'ADA Lemon Lime'],
      image: 'https://images.pexels.com/photos/50593/coca-cola-cold-drink-soft-drink-coke-50593.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop',
      endDate: '2025-03-31',
      featured: true,
      color: 'from-blue-500 to-blue-600'
    },
    {
      id: 2,
      title: 'Snack Attack Special',
      description: 'Buy 2 get 1 free on all Happy Snacks products',
      discount: 'Buy 2 Get 1 FREE',
      products: ['Happy Chips Original', 'Happy Nuts Mix', 'Happy Crackers'],
      image: 'https://images.pexels.com/photos/209206/pexels-photo-209206.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop',
      endDate: '2025-02-28',
      featured: false,
      color: 'from-orange-500 to-orange-600'
    },
    {
      id: 3,
      title: 'Household Essentials Deal',
      description: 'Save big on Island Care cleaning products',
      discount: '30% OFF',
      products: ['Island Care Detergent', 'Island Care Fabric Softener'],
      image: 'https://images.pexels.com/photos/4239091/pexels-photo-4239091.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop',
      endDate: '2025-04-15',
      featured: true,
      color: 'from-green-500 to-green-600'
    },
    {
      id: 4,
      title: 'Fresh & Natural Bundle',
      description: 'Tropical Fresh juice combo at unbeatable prices',
      discount: '25% OFF',
      products: ['Tropical Mango Juice', 'Tropical Pineapple Juice'],
      image: 'https://images.pexels.com/photos/96974/pexels-photo-96974.jpeg?auto=compress&cs=tinysrgb&w=600&h=400&fit=crop',
      endDate: '2025-03-15',
      featured: false,
      color: 'from-yellow-500 to-yellow-600'
    }
  ];

  // Auto-advance slides
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % promotions.length);
    }, 5000);

    return () => clearInterval(timer);
  }, [promotions.length]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % promotions.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + promotions.length) % promotions.length);
  };

  const getTimeRemaining = (endDate: string) => {
    const end = new Date(endDate).getTime();
    const now = new Date().getTime();
    const difference = end - now;

    if (difference > 0) {
      const days = Math.floor(difference / (1000 * 60 * 60 * 24));
      const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      return { days, hours, expired: false };
    }

    return { days: 0, hours: 0, expired: true };
  };

  return (
    <section id="promotions" className="py-16 sm:py-20 lg:py-24 bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-diagonal-pattern opacity-10"></div>
      
      {/* Floating Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-accent-400 rounded-full opacity-40"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [-10, 10, -10],
              opacity: [0.2, 0.6, 0.2],
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      <div className="container mx-auto px-6 sm:px-8 relative z-10">
        <motion.div
          ref={ref}
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-16 sm:mb-20"
        >
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-heading font-bold text-white mb-6 sm:mb-8">
            Hot Deals & Promotions
          </h2>
          <p className="text-lg sm:text-xl text-neutral-200 max-w-4xl mx-auto leading-relaxed px-4">
            Don't miss out on our exclusive offers and limited-time deals on your favorite products.
          </p>
        </motion.div>

        {/* Main Carousel */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="relative mb-16 sm:mb-20"
        >
          <div className="relative h-80 sm:h-96 md:h-[500px] rounded-3xl overflow-hidden">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentSlide}
                initial={{ opacity: 0, x: 300 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -300 }}
                transition={{ duration: 0.5 }}
                className="absolute inset-0"
              >
                <div className={`absolute inset-0 bg-gradient-to-r ${promotions[currentSlide].color}`}>
                  <img
                    src={promotions[currentSlide].image}
                    alt={promotions[currentSlide].title}
                    className="w-full h-full object-cover mix-blend-overlay opacity-30"
                  />
                </div>

                <div className="absolute inset-0 bg-gradient-to-r from-black/50 to-transparent"></div>

                <div className="relative h-full flex items-center">
                  <div className="container mx-auto px-6 sm:px-8">
                    <div className="max-w-2xl">
                      {/* Badge */}
                      <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.2 }}
                        className="inline-flex items-center space-x-2 bg-accent-500/20 backdrop-blur-sm border border-accent-400/30 rounded-full px-4 py-2 mb-4 sm:mb-6"
                      >
                        {promotions[currentSlide].featured && (
                          <>
                            <Star className="w-4 h-4 text-accent-400" />
                            <span className="text-accent-400 text-sm font-medium">Featured Deal</span>
                          </>
                        )}
                        {!promotions[currentSlide].featured && (
                          <>
                            <Zap className="w-4 h-4 text-accent-400" />
                            <span className="text-accent-400 text-sm font-medium">Limited Time</span>
                          </>
                        )}
                      </motion.div>

                      {/* Title */}
                      <motion.h3
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.3 }}
                        className="text-2xl sm:text-4xl md:text-5xl lg:text-6xl font-heading font-bold text-white mb-3 sm:mb-4"
                      >
                        {promotions[currentSlide].title}
                      </motion.h3>

                      {/* Description */}
                      <motion.p
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.4 }}
                        className="text-base sm:text-lg md:text-xl text-neutral-200 mb-4 sm:mb-6"
                      >
                        {promotions[currentSlide].description}
                      </motion.p>

                      {/* Discount Badge */}
                      <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.5 }}
                        className="inline-block bg-accent-500 text-white px-4 sm:px-6 py-2 sm:py-3 rounded-full text-lg sm:text-xl md:text-2xl font-bold mb-4 sm:mb-6 shadow-lg"
                      >
                        {promotions[currentSlide].discount}
                      </motion.div>

                      {/* Products */}
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.6 }}
                        className="mb-4 sm:mb-6"
                      >
                        <p className="text-neutral-300 mb-2 text-sm sm:text-base">Includes:</p>
                        <div className="flex flex-wrap gap-2">
                          {promotions[currentSlide].products.map((product, index) => (
                            <span
                              key={index}
                              className="px-3 py-1 bg-white/20 backdrop-blur-sm text-white rounded-full text-xs sm:text-sm"
                            >
                              {product}
                            </span>
                          ))}
                        </div>
                      </motion.div>

                      {/* Countdown */}
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.7 }}
                        className="flex items-center space-x-4 mb-6 sm:mb-8"
                      >
                        <Clock className="w-5 h-5 text-accent-400" />
                        <div className="text-white text-sm sm:text-base">
                          {(() => {
                            const timeLeft = getTimeRemaining(promotions[currentSlide].endDate);
                            return timeLeft.expired ? (
                              <span className="text-red-400">Offer Expired</span>
                            ) : (
                              <span>
                                {timeLeft.days} days, {timeLeft.hours} hours remaining
                              </span>
                            );
                          })()}
                        </div>
                      </motion.div>

                      {/* CTA */}
                      <motion.button
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.8 }}
                        className="group bg-accent-500 hover:bg-accent-600 text-white px-6 sm:px-8 py-3 sm:py-4 rounded-full font-semibold text-base sm:text-lg transition-all duration-300 flex items-center space-x-2"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <span>Shop Now</span>
                        <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                      </motion.button>
                    </div>
                  </div>
                </div>
              </motion.div>
            </AnimatePresence>

            {/* Navigation Arrows */}
            <button
              onClick={prevSlide}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white p-3 rounded-full transition-all duration-300"
            >
              <ChevronLeft className="w-6 h-6" />
            </button>
            <button
              onClick={nextSlide}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white p-3 rounded-full transition-all duration-300"
            >
              <ChevronRight className="w-6 h-6" />
            </button>

            {/* Slide Indicators */}
            <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-2">
              {promotions.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentSlide(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === currentSlide 
                      ? 'bg-accent-500 scale-125' 
                      : 'bg-white/50 hover:bg-white/70'
                  }`}
                />
              ))}
            </div>
          </div>
        </motion.div>

        {/* Additional Promotions Grid */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="grid sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8"
        >
          {promotions.slice(1).map((promo, index) => (
            <motion.div
              key={promo.id}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={inView ? { opacity: 1, scale: 1 } : {}}
              transition={{ duration: 0.6, delay: 0.6 + index * 0.1 }}
              className="group bg-white/10 backdrop-blur-sm rounded-2xl overflow-hidden border border-white/20 hover:border-accent-400/50 transition-all duration-300"
            >
              <div className="relative h-48 sm:h-56 overflow-hidden">
                <img
                  src={promo.image}
                  alt={promo.title}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                <div className="absolute top-3 right-3">
                  <span className="bg-accent-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                    {promo.discount}
                  </span>
                </div>
              </div>

              <div className="p-6 sm:p-8">
                <h4 className="text-lg sm:text-xl font-heading font-bold text-white mb-2 group-hover:text-accent-400 transition-colors">
                  {promo.title}
                </h4>
                <p className="text-neutral-300 text-sm mb-4">
                  {promo.description}
                </p>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 text-neutral-400 text-sm">
                    <Clock className="w-4 h-4" />
                    <span>
                      {(() => {
                        const timeLeft = getTimeRemaining(promo.endDate);
                        return `${timeLeft.days}d ${timeLeft.hours}h left`;
                      })()}
                    </span>
                  </div>
                  <button className="text-accent-400 hover:text-accent-300 font-semibold text-sm transition-colors">
                    View Deal
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default Promotions;