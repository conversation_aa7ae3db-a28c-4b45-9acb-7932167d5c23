import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { Search, Filter, Grid, List, X, ZoomIn, Package, MapPin, Scale } from 'lucide-react';
import { products, brands, categories } from '../data/brands';

const Products: React.FC = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedBrand, setSelectedBrand] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedProduct, setSelectedProduct] = useState<typeof products[0] | null>(null);
  const [zoomedImage, setZoomedImage] = useState<string | null>(null);

  const filteredProducts = products.filter(product => {
    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;
    const matchesBrand = selectedBrand === 'all' || product.brand === selectedBrand;
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.brand.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesBrand && matchesSearch;
  });

  const handleImageClick = (imageSrc: string) => {
    setZoomedImage(imageSrc);
  };

  const handleTitleClick = (product: typeof products[0]) => {
    setZoomedImage(product.image);
  };

  return (
    <section id="products" className="py-16 sm:py-20 lg:py-24 bg-white relative overflow-hidden">
      <div className="container mx-auto px-6 sm:px-8 relative z-10">
        <motion.div
          ref={ref}
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-16 sm:mb-20"
        >
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-heading font-bold text-primary-900 mb-6 sm:mb-8">
            Product Catalog
          </h2>
          <p className="text-lg sm:text-xl text-neutral-600 max-w-4xl mx-auto leading-relaxed px-4">
            Explore our comprehensive range of quality products from trusted brands across Jamaica.
          </p>
        </motion.div>

        {/* Filters and Search */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="bg-neutral-50 rounded-2xl p-6 sm:p-8 mb-8 sm:mb-10"
        >
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 items-end">
            {/* Search */}
            <div className="sm:col-span-2 lg:col-span-1">
              <label className="block text-sm font-semibold text-neutral-700 mb-2">
                Search Products
              </label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Search products or brands..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-neutral-200 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-transparent transition-all text-sm sm:text-base"
                />
              </div>
            </div>

            {/* Category Filter */}
            <div>
              <label className="block text-sm font-semibold text-neutral-700 mb-2">
                Category
              </label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full px-4 py-3 border border-neutral-200 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-transparent transition-all text-sm sm:text-base"
              >
                <option value="all">All Categories</option>
                {categories.slice(1).map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Brand Filter */}
            <div>
              <label className="block text-sm font-semibold text-neutral-700 mb-2">
                Brand
              </label>
              <select
                value={selectedBrand}
                onChange={(e) => setSelectedBrand(e.target.value)}
                className="w-full px-4 py-3 border border-neutral-200 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-transparent transition-all text-sm sm:text-base"
              >
                <option value="all">All Brands</option>
                {brands.map((brand) => (
                  <option key={brand.id} value={brand.name}>
                    {brand.name}
                  </option>
                ))}
              </select>
            </div>

            {/* View Mode */}
            <div>
              <label className="block text-sm font-semibold text-neutral-700 mb-2">
                View
              </label>
              <div className="flex bg-white border border-neutral-200 rounded-lg overflow-hidden">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`flex-1 px-4 py-3 flex items-center justify-center transition-colors ${
                    viewMode === 'grid' 
                      ? 'bg-accent-500 text-white' 
                      : 'text-neutral-600 hover:bg-neutral-50'
                  }`}
                >
                  <Grid className="w-5 h-5" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`flex-1 px-4 py-3 flex items-center justify-center transition-colors ${
                    viewMode === 'list' 
                      ? 'bg-accent-500 text-white' 
                      : 'text-neutral-600 hover:bg-neutral-50'
                  }`}
                >
                  <List className="w-5 h-5" />
                </button>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Results Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="flex items-center justify-between mb-8 sm:mb-10"
        >
          <div>
            <h3 className="text-xl sm:text-2xl font-heading font-bold text-primary-900">
              {filteredProducts.length} Product{filteredProducts.length !== 1 ? 's' : ''} Found
            </h3>
            <p className="text-neutral-600 text-sm sm:text-base">
              {selectedCategory !== 'all' && `${categories.find(c => c.id === selectedCategory)?.name} • `}
              {selectedBrand !== 'all' && `${selectedBrand} • `}
              Showing all results
            </p>
          </div>
        </motion.div>

        {/* Products Grid/List */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={inView ? { opacity: 1 } : {}}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <AnimatePresence mode="wait">
            {viewMode === 'grid' ? (
              <motion.div
                key="grid"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="grid sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 sm:gap-8"
              >
                {filteredProducts.map((product, index) => (
                  <motion.div
                    key={product.id}
                    layout
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5, delay: index * 0.05 }}
                    className="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border border-neutral-100 hover:border-accent-200"
                  >
                    {/* Product Image */}
                    <div className="relative overflow-hidden cursor-pointer" onClick={() => handleImageClick(product.image)}>
                      <img
                        src={product.image}
                        alt={product.name}
                        className="w-full h-48 sm:h-56 object-cover group-hover:scale-110 transition-transform duration-500"
                      />
                      
                      {/* Zoom Overlay */}
                      <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                        <ZoomIn className="w-8 h-8 text-white" />
                      </div>
                    </div>

                    {/* Product Info */}
                    <div className="p-6 sm:p-8">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <button
                            onClick={() => handleTitleClick(product)}
                            className="text-base sm:text-lg font-heading font-bold text-primary-900 mb-1 hover:text-accent-600 transition-colors cursor-pointer text-left w-full group-hover:text-accent-600 hover:shadow-lg hover:shadow-accent-500/20 rounded px-1 py-1 -mx-1 -my-1"
                          >
                            {product.name}
                          </button>
                          <p className="text-sm text-accent-600 font-medium">
                            {product.brand}
                          </p>
                        </div>
                        <span className="px-3 py-1 bg-neutral-100 text-neutral-600 rounded-full text-xs font-medium flex-shrink-0 ml-2">
                          {categories.find(c => c.id === product.category)?.name}
                        </span>
                      </div>
                      
                      <p className="text-neutral-600 text-sm mb-4 leading-relaxed line-clamp-2">
                        {product.description}
                      </p>
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            ) : (
              <motion.div
                key="list"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="space-y-4 sm:space-y-6"
              >
                {filteredProducts.map((product, index) => (
                  <motion.div
                    key={product.id}
                    layout
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.05 }}
                    className="group bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-neutral-100 hover:border-accent-200"
                  >
                    <div className="flex flex-col sm:flex-row items-start p-6 sm:p-8">
                      {/* Product Image */}
                      <div 
                        className="w-full sm:w-24 sm:h-24 h-48 rounded-xl overflow-hidden flex-shrink-0 mb-4 sm:mb-0 sm:mr-6 relative cursor-pointer"
                        onClick={() => handleImageClick(product.image)}
                      >
                        <img
                          src={product.image}
                          alt={product.name}
                          className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                        />
                        <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                          <ZoomIn className="w-6 h-6 text-white" />
                        </div>
                      </div>

                      {/* Product Info */}
                      <div className="flex-1">
                        <div className="flex flex-col sm:flex-row items-start justify-between mb-2">
                          <div className="mb-2 sm:mb-0">
                            <button
                              onClick={() => handleTitleClick(product)}
                              className="text-lg sm:text-xl font-heading font-bold text-primary-900 mb-1 hover:text-accent-600 transition-colors cursor-pointer text-left group-hover:text-accent-600 hover:shadow-lg hover:shadow-accent-500/20 rounded px-1 py-1 -mx-1 -my-1"
                            >
                              {product.name}
                            </button>
                            <p className="text-accent-600 font-medium text-sm sm:text-base">
                              {product.brand}
                            </p>
                          </div>
                          <span className="px-3 py-1 bg-neutral-100 text-neutral-600 rounded-full text-sm font-medium">
                            {categories.find(c => c.id === product.category)?.name}
                          </span>
                        </div>
                        
                        <p className="text-neutral-600 mb-3 leading-relaxed text-sm sm:text-base line-clamp-2">
                          {product.description}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>

        {/* No Results */}
        {filteredProducts.length === 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center py-16 sm:py-20"
          >
            <div className="w-24 h-24 bg-neutral-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Search className="w-12 h-12 text-neutral-400" />
            </div>
            <h3 className="text-xl sm:text-2xl font-heading font-bold text-neutral-700 mb-4">
              No Products Found
            </h3>
            <p className="text-neutral-600 mb-8 text-sm sm:text-base">
              Try adjusting your search criteria or browse all products.
            </p>
            <button
              onClick={() => {
                setSearchTerm('');
                setSelectedCategory('all');
                setSelectedBrand('all');
              }}
              className="bg-accent-500 hover:bg-accent-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors text-sm sm:text-base"
            >
              Clear Filters
            </button>
          </motion.div>
        )}

        {/* Image Zoom Modal */}
        <AnimatePresence>
          {zoomedImage && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
              onClick={() => setZoomedImage(null)}
            >
              <motion.div
                initial={{ opacity: 0, scale: 0.5 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.5 }}
                className="relative max-w-4xl max-h-[90vh] w-full h-full flex items-center justify-center"
                onClick={(e) => e.stopPropagation()}
              >
                <img
                  src={zoomedImage}
                  alt="Product Image"
                  className="max-w-full max-h-full object-contain rounded-lg shadow-2xl"
                />
                <button
                  onClick={() => setZoomedImage(null)}
                  className="absolute top-4 right-4 p-3 bg-white/20 hover:bg-white/30 rounded-full transition-colors backdrop-blur-sm"
                >
                  <X className="w-6 h-6 text-white" />
                </button>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Product Detail Modal */}
        <AnimatePresence>
          {selectedProduct && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
              onClick={() => setSelectedProduct(null)}
            >
              <motion.div
                initial={{ opacity: 0, scale: 0.8, y: 50 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.8, y: 50 }}
                className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
                onClick={(e) => e.stopPropagation()}
              >
                {/* Modal Header */}
                <div className="relative">
                  <img
                    src={selectedProduct.image}
                    alt={selectedProduct.name}
                    className="w-full h-64 sm:h-80 object-cover cursor-pointer"
                    onClick={() => handleImageClick(selectedProduct.image)}
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                  <button
                    onClick={() => setSelectedProduct(null)}
                    className="absolute top-4 right-4 p-2 bg-white/20 hover:bg-white/30 rounded-lg transition-colors"
                  >
                    <X className="w-6 h-6 text-white" />
                  </button>
                  <div className="absolute bottom-4 left-4 right-4 text-white">
                    <div className="flex items-center space-x-3 mb-2">
                      <span className="px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-sm font-medium">
                        {selectedProduct.brand}
                      </span>
                      <span className="px-3 py-1 bg-accent-500 rounded-full text-sm font-medium">
                        {categories.find(c => c.id === selectedProduct.category)?.name}
                      </span>
                    </div>
                    <h2 className="text-2xl sm:text-3xl font-heading font-bold">{selectedProduct.name}</h2>
                  </div>
                </div>

                {/* Modal Content */}
                <div className="p-6 sm:p-8 max-h-96 overflow-y-auto">
                  <div className="grid md:grid-cols-2 gap-8">
                    {/* Product Details */}
                    <div>
                      <h3 className="text-xl font-heading font-bold text-primary-900 mb-4">Product Details</h3>
                      <p className="text-neutral-600 leading-relaxed mb-6">
                        {selectedProduct.description}
                      </p>
                      
                      {/* Product Specifications */}
                      <div className="space-y-4">
                        {selectedProduct.ingredients && (
                          <div className="flex items-start space-x-3">
                            <Package className="w-5 h-5 text-accent-500 mt-1 flex-shrink-0" />
                            <div>
                              <span className="font-semibold text-primary-900">Ingredients:</span>
                              <p className="text-neutral-600">{selectedProduct.ingredients}</p>
                            </div>
                          </div>
                        )}
                        
                        {selectedProduct.weight && (
                          <div className="flex items-start space-x-3">
                            <Scale className="w-5 h-5 text-accent-500 mt-1 flex-shrink-0" />
                            <div>
                              <span className="font-semibold text-primary-900">Net Weight:</span>
                              <p className="text-neutral-600">{selectedProduct.weight}</p>
                            </div>
                          </div>
                        )}
                        
                        {selectedProduct.origin && (
                          <div className="flex items-start space-x-3">
                            <MapPin className="w-5 h-5 text-accent-500 mt-1 flex-shrink-0" />
                            <div>
                              <span className="font-semibold text-primary-900">Origin:</span>
                              <p className="text-neutral-600">{selectedProduct.origin}</p>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Brand Information */}
                    <div>
                      <h3 className="text-xl font-heading font-bold text-primary-900 mb-4">About {selectedProduct.brand}</h3>
                      
                      {/* Brand Information */}
                      <div className="bg-neutral-50 rounded-xl p-6">
                        <div className="flex items-center space-x-4 mb-3">
                          <img
                            src={brands.find(b => b.name === selectedProduct.brand)?.logo}
                            alt={selectedProduct.brand}
                            className="w-12 h-12 object-contain rounded-lg bg-white p-2"
                          />
                          <div>
                            <h5 className="font-semibold text-primary-900">{selectedProduct.brand}</h5>
                            <p className="text-sm text-neutral-600">
                              {brands.find(b => b.name === selectedProduct.brand)?.description}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </section>
  );
};

export default Products;