import React from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { TrendingUp, Truck, Warehouse, Users, Package, Navigation, ShoppingCart, Mail, CheckCircle, Heart } from 'lucide-react';
import { Link } from 'react-router-dom';
import SEO from '../components/common/SEO';

const Distribution: React.FC = () => {
  const [heroRef, heroInView] = useInView({ triggerOnce: true, threshold: 0.1 });
  const [servicesRef, servicesInView] = useInView({ triggerOnce: true, threshold: 0.1 });
  const [processRef, processInView] = useInView({ triggerOnce: true, threshold: 0.1 });

  const distributionServices = [
    {
      icon: TrendingUp,
      title: 'SALES & MARKETING',
      description: 'We help our customers to achieve their sales targets through the provision of customized Sales & Marketing services. All EVJValuedCustomer has the potential to benefit from our limited Special Time Offers or Blitz Promotions throughout the year. Everyday Value Jamaica has taken this integrated approach which serves not only our suppliers but also our retailers, island-wide.',
      features: [
        'Customized Sales & Marketing Services',
        'Special Time Offers & Blitz Promotions',
        'Integrated Supplier & Retailer Support',
        'Island-wide Marketing Reach'
      ]
    },
    {
      icon: Truck,
      title: 'DELIVERY & MERCHANDISING',
      description: 'In almost every town and nearby community across Jamaica, we deliver high quality consumer products such as: Foods, Beverages, Confectioneries, Personal Care, Household Supplies, Car Accessories and Pet Supplies. Our highly trained and proficient merchandisers maintain the highest level of standard when placing goods and replenishing stocks.',
      features: [
        'Island-wide Delivery Coverage',
        'High Quality Consumer Products',
        'Professional Merchandising Services',
        'Stock Replenishment & Management',
        'Multiple Product Categories'
      ]
    },
    {
      icon: Warehouse,
      title: 'WAREHOUSING & CUSTOMER SERVICE',
      description: 'For best practices in warehouse picking and packing, organizing and inventory management, our team exercises the best methods to achieve the most reliable and up-to-date information necessary to meet and exceed customer expectations. Our in-house customer support makes it easy for all EVJValuedCustomer to query and make adjustments to orders.',
      features: [
        'Best Practice Warehouse Operations',
        'Professional Picking & Packing',
        'Advanced Inventory Management',
        'In-house Customer Support Platform',
        'Order Query & Adjustment Services'
      ]
    }
  ];



  return (
    <>
      <SEO
        title="Distribution Services - EverydayValueJamaica"
        description="Comprehensive distribution services across Jamaica including sales & marketing, delivery & merchandising, and warehousing & customer service."
        keywords="distribution Jamaica, sales marketing, delivery merchandising, warehousing customer service, islandwide distribution"
      />

      {/* Hero Section */}
      <section className="py-16 sm:py-20 lg:py-24 bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700 text-white">
        <div className="container mx-auto px-6 sm:px-8">
          <motion.div
            ref={heroRef}
            initial={{ opacity: 0, y: 50 }}
            animate={heroInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2, duration: 0.6 }}
              className="inline-flex items-center space-x-2 bg-accent-500/20 backdrop-blur-sm border border-accent-400/30 rounded-full px-6 py-3 mb-8"
            >
              <Package className="w-5 h-5 text-accent-400" />
              <span className="text-accent-400 font-medium">Distribution Excellence</span>
            </motion.div>

            <motion.h1
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.8 }}
              className="text-4xl sm:text-5xl md:text-6xl font-heading font-bold mb-8 leading-tight"
            >
              Distribution Services
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.8 }}
              className="text-xl sm:text-2xl text-neutral-200 max-w-4xl mx-auto leading-relaxed mb-12"
            >
              State-of-the-art distribution facilities with cutting-edge technology for sales & marketing,
              delivery & merchandising, and warehousing & customer service across Jamaica.
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8, duration: 0.8 }}
              className="flex flex-col sm:flex-row gap-4 justify-center"
            >
              <Link
                to="/contact"
                className="inline-flex items-center space-x-3 bg-accent-500 hover:bg-accent-600 text-white px-8 py-4 rounded-full font-semibold text-lg transition-all duration-300"
              >
                <Truck className="w-6 h-6" />
                <span>Start Distribution</span>
              </Link>
              <Link
                to="/manufacturing"
                className="inline-flex items-center space-x-3 border-2 border-white text-white hover:bg-white hover:text-primary-900 px-8 py-4 rounded-full font-semibold text-lg transition-all duration-300"
              >
                <Package className="w-6 h-6" />
                <span>View Manufacturing</span>
              </Link>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Distribution Services */}
      <section className="py-16 sm:py-20 lg:py-24 bg-white">
        <div className="container mx-auto px-6 sm:px-8">
          <motion.div
            ref={servicesRef}
            initial={{ opacity: 0, y: 50 }}
            animate={servicesInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl sm:text-4xl md:text-5xl font-heading font-bold text-primary-900 mb-6">
              Our Distribution Services
            </h2>
            <p className="text-lg sm:text-xl text-neutral-600 max-w-4xl mx-auto leading-relaxed">
              Comprehensive distribution solutions designed to get your products to market efficiently and reliably.
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-3 gap-8">
            {distributionServices.map((service, index) => (
              <motion.div
                key={service.title}
                initial={{ opacity: 0, y: 30 }}
                animate={servicesInView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-neutral-50 rounded-3xl p-8 hover:shadow-lg transition-shadow h-full flex flex-col"
              >
                <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-accent-500 to-accent-600 flex items-center justify-center mb-6">
                  <service.icon className="w-8 h-8 text-white" />
                </div>

                <h3 className="text-lg sm:text-xl lg:text-2xl font-heading font-bold text-primary-900 mb-4 leading-tight min-h-[3.5rem] flex items-center">
                  {service.title}
                </h3>

                <p className="text-neutral-600 leading-relaxed mb-6 flex-grow">
                  {service.description}
                </p>

                <ul className="space-y-3">
                  {service.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start space-x-3">
                      <CheckCircle className="w-5 h-5 text-accent-500 flex-shrink-0 mt-0.5" />
                      <span className="text-neutral-700 font-medium text-sm leading-relaxed">{feature}</span>
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Happy Customers Showcase */}
      <section className="py-16 sm:py-20 lg:py-24 bg-gradient-to-br from-neutral-50 to-white">
        <div className="container mx-auto px-6 sm:px-8">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <div className="inline-flex items-center space-x-2 bg-accent-500/10 backdrop-blur-sm border border-accent-400/20 rounded-full px-6 py-3 mb-6">
              <Users className="w-5 h-5 text-accent-600" />
              <span className="text-accent-600 font-medium">Trusted Partners</span>
            </div>
            <h2 className="text-3xl sm:text-4xl md:text-5xl font-heading font-bold text-primary-900 mb-6">
              Our Happy Customers
            </h2>
            <p className="text-lg sm:text-xl text-neutral-600 max-w-4xl mx-auto leading-relaxed">
              Proud to serve leading supermarkets and pharmacies across Jamaica with reliable distribution services.
            </p>
          </motion.div>

          {/* Supermarkets Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="mb-20"
          >
            <div className="flex items-center justify-center mb-12">
              <div className="flex items-center space-x-3 bg-gradient-to-r from-primary-600 to-primary-700 text-white px-8 py-4 rounded-full shadow-lg">
                <ShoppingCart className="w-6 h-6" />
                <h3 className="text-2xl font-heading font-bold">Supermarkets</h3>
              </div>
            </div>

            <div className="flex flex-wrap items-center justify-center gap-8 lg:gap-12">
              {[
                { name: 'Progressive Grocers', image: '/images/Happy customers Supermarket/Progressive grocers.png' },
                { name: 'Shoppers Fair', image: '/images/Happy customers Supermarket/Shoppers fair logo.png' },
                { name: 'General Foods', image: '/images/Happy customers Supermarket/General foods logo.png' },
                { name: 'Boot Stores', image: '/images/Happy customers Supermarket/boot logo.jpg' },
                { name: 'Fresh Foods', image: '/images/Happy customers Supermarket/fresh foods logo.jpg' },
                { name: 'MegaMart', image: '/images/Happy customers Supermarket/megamart.jpg' },
                { name: 'JRW', image: '/images/Happy customers Supermarket/JRW logo.jpg' }
              ].map((customer, index) => (
                <motion.div
                  key={customer.name}
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ scale: 1.1 }}
                  className="group cursor-pointer"
                >
                  <div className="w-40 h-32 lg:w-48 lg:h-36 flex items-center justify-center p-4 transition-all duration-300 group-hover:drop-shadow-lg">
                    <img
                      src={customer.image}
                      alt={customer.name}
                      className="max-w-full max-h-full object-contain transition-all duration-300"
                    />
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Pharmacies Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center justify-center mb-12">
              <div className="flex items-center space-x-3 bg-gradient-to-r from-accent-500 to-accent-600 text-white px-8 py-4 rounded-full shadow-lg">
                <Heart className="w-6 h-6" />
                <h3 className="text-2xl font-heading font-bold">Pharmacies</h3>
              </div>
            </div>

            <div className="flex flex-wrap items-center justify-center gap-12 lg:gap-16">
              {[
                { name: 'Fontana Pharmacy', image: '/images/Happy customers pharmacy/Fontana Jamaica Logo 160 x 65.png' },
                { name: 'Lees Family Pharmacy', image: '/images/Happy customers pharmacy/lees pharmacy logo.png' },
                { name: 'Supermad Pharmacy', image: '/images/Happy customers pharmacy/supermad logo.jpg' }
              ].map((customer, index) => (
                <motion.div
                  key={customer.name}
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.6, delay: index * 0.15 }}
                  viewport={{ once: true }}
                  whileHover={{ scale: 1.1 }}
                  className="group cursor-pointer"
                >
                  <div className="w-48 h-36 lg:w-56 lg:h-40 flex items-center justify-center p-6 transition-all duration-300 group-hover:drop-shadow-lg">
                    <img
                      src={customer.image}
                      alt={customer.name}
                      className="max-w-full max-h-full object-contain transition-all duration-300"
                    />
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 sm:py-20 lg:py-24 bg-gradient-to-br from-primary-900 to-primary-800 text-white">
        <div className="container mx-auto px-6 sm:px-8">
          <motion.div
            ref={processRef}
            initial={{ opacity: 0, y: 50 }}
            animate={processInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            <Truck className="w-16 h-16 text-accent-400 mx-auto mb-6" />
            <h2 className="text-3xl sm:text-4xl font-heading font-bold mb-6">
              Ready to Start Distribution?
            </h2>
            <p className="text-xl text-neutral-200 leading-relaxed mb-8">
              Let's discuss your distribution needs and create a custom solution that gets your products to market efficiently.
            </p>
            <Link
              to="/contact"
              className="inline-flex items-center space-x-3 bg-accent-500 hover:bg-accent-600 text-white px-8 py-4 rounded-full font-semibold text-lg transition-all duration-300"
            >
              <Users className="w-6 h-6" />
              <span>Get Started</span>
            </Link>
          </motion.div>
        </div>
      </section>
    </>
  );
};

export default Distribution;
