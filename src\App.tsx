import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { useLocation } from 'react-router-dom';

// Layout Components
import Header from './components/layout/Header';
import Footer from './components/layout/Footer';
import AIChatbot from './components/ai/AIChatbot';

// Pages
import Home from './pages/Home';
import Brands from './pages/Brands';
import Products from './pages/Products';
import Services from './pages/Services';
import Careers from './pages/Careers';
import News from './pages/News';
import Contact from './pages/Contact';
import NotFound from './pages/NotFound';
import WhoWeAre from './pages/WhoWeAre';
import WhatWeDo from './pages/WhatWeDo';
import Manufacturing from './pages/Manufacturing';
import Distribution from './pages/Distribution';


function App() {
  const location = useLocation();

  return (
    <div className="min-h-screen bg-white">
      <Header />
      <AnimatePresence mode="wait">
        <motion.main
          key={location.pathname}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
          className="pt-32"
        >
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/who-we-are" element={<WhoWeAre />} />
            <Route path="/what-we-do" element={<WhatWeDo />} />
            <Route path="/manufacturing" element={<Manufacturing />} />
            <Route path="/distribution" element={<Distribution />} />
            <Route path="/brands" element={<Brands />} />
            <Route path="/careers" element={<Careers />} />
            <Route path="/contact" element={<Contact />} />
            {/* Keep additional routes for direct access */}
            <Route path="/products" element={<Products />} />
            <Route path="/services" element={<Services />} />

            <Route path="/news" element={<News />} />
            <Route path="*" element={<NotFound />} />
          </Routes>
        </motion.main>
      </AnimatePresence>
      <Footer />
      <AIChatbot />
    </div>
  );
}

export default App;