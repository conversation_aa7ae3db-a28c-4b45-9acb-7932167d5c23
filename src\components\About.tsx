import React from 'react';

const stats = [
  { value: '50+', label: 'Years in Business' },
  { value: '100+', label: 'Brands Supported' },
  { value: '✓', label: 'Islandwide Delivery' },
  { value: '300+', label: 'Happy Customers' },
];

const About: React.FC = () => (
  <section className="w-full bg-[#f1eae4] py-16 px-4 sm:px-8">
    <div className="max-w-7xl mx-auto flex flex-col lg:flex-row items-center gap-12 lg:gap-16">
      {/* Video Column - Clean, Responsive Video */}
      <div className="w-full lg:w-1/2 flex-shrink-0">
        <video
          src="/intro-video.mp4"
          controls
          autoPlay
          muted
          loop
          className="w-full rounded-lg shadow-lg"
        />
      </div>
      {/* Content Column */}
      <div className="w-full lg:w-1/2 flex flex-col items-start">
        <h2 className="text-3xl sm:text-4xl font-bold text-[#0a0a23] mb-4">Our Story</h2>
        <p className="text-lg text-[#0a0a23] mb-6 max-w-xl">
          EverydayValueJamaica has been a driving force in Jamaica's distribution landscape for over 50 years. We've built trusted relationships, supported local brands, and ensured that quality products reach customers islandwide — one delivery at a time.
        </p>
        <span className="inline-block bg-yellow-400 text-[#0a0a23] font-semibold rounded-full px-5 py-2 mb-8 shadow text-lg">
          98% Customer Satisfaction
        </span>
        {/* Stats Row */}
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 w-full mt-auto">
          {stats.map((stat, idx) => (
            <div
              key={idx}
              className="bg-white rounded-xl shadow p-5 flex flex-col items-center justify-center min-w-[120px]"
            >
              <div className="text-2xl font-bold text-yellow-500 mb-1">{stat.value}</div>
              <div className="text-xs text-[#0a0a23] text-center font-medium">{stat.label}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  </section>
);

export default About;