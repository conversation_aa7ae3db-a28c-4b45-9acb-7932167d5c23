import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { Search, Filter, Star, ArrowRight, X } from 'lucide-react';
import { brands, categories, products } from '../data/brands';

interface BrandsProps {
  onNavigate: (section: string) => void;
}

const Brands: React.FC<BrandsProps> = ({ onNavigate }) => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedBrand, setSelectedBrand] = useState<string | null>(null);

  const filteredBrands = brands.filter(brand => {
    const matchesCategory = selectedCategory === 'all' || brand.category.includes(selectedCategory);
    const matchesSearch = brand.name.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const brandProducts = selectedBrand ? products.filter(product => product.brand === selectedBrand) : [];

  return (
    <section id="brands" className="py-16 sm:py-20 lg:py-24 bg-gradient-to-br from-neutral-50 to-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-diagonal-pattern opacity-5"></div>
      
      <div className="container mx-auto px-6 sm:px-8 relative z-10">
        <motion.div
          ref={ref}
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-16 sm:mb-20"
        >
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-heading font-bold text-primary-900 mb-6 sm:mb-8">
            Our Premium Brands
          </h2>
          <p className="text-lg sm:text-xl text-neutral-600 max-w-4xl mx-auto leading-relaxed px-4">
            Discover the trusted brands that make EverydayValueJamaica Jamaica's preferred distribution partner.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-4 gap-8 sm:gap-10">
          {/* Sidebar Filters */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={inView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="lg:col-span-1"
          >
            <div className="bg-white rounded-2xl shadow-lg p-6 sm:p-8 sticky top-24">
              {/* Search */}
              <div className="mb-8">
                <label className="block text-sm font-semibold text-neutral-700 mb-3">
                  Search Brands
                </label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-5 h-5" />
                  <input
                    type="text"
                    placeholder="Search brands..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 border border-neutral-200 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-transparent transition-all text-sm sm:text-base"
                  />
                </div>
              </div>

              {/* Category Filter */}
              <div>
                <label className="block text-sm font-semibold text-neutral-700 mb-3">
                  <Filter className="inline w-4 h-4 mr-2" />
                  Categories
                </label>
                <div className="space-y-2">
                  {categories.map((category) => (
                    <motion.button
                      key={category.id}
                      onClick={() => setSelectedCategory(category.id)}
                      className={`w-full text-left px-4 py-3 rounded-lg transition-all duration-200 text-sm sm:text-base ${
                        selectedCategory === category.id
                          ? 'bg-accent-500 text-white shadow-md'
                          : 'bg-neutral-50 text-neutral-700 hover:bg-neutral-100'
                      }`}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <span className="font-medium">{category.name}</span>
                    </motion.button>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* Results Header */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-8 sm:mb-10 space-y-4 sm:space-y-0"
            >
              <div>
                <h3 className="text-xl sm:text-2xl font-heading font-bold text-primary-900">
                  {filteredBrands.length} Brand{filteredBrands.length !== 1 ? 's' : ''} Found
                </h3>
                <p className="text-neutral-600 text-sm sm:text-base">
                  {selectedCategory !== 'all' && `Filtered by ${categories.find(c => c.id === selectedCategory)?.name}`}
                </p>
              </div>
              <motion.button
                onClick={() => onNavigate('products')}
                className="hidden md:flex items-center space-x-2 text-accent-500 hover:text-accent-600 font-semibold text-sm sm:text-base"
                whileHover={{ x: 5 }}
              >
                <span>View All Products</span>
                <ArrowRight className="w-4 h-4" />
              </motion.button>
            </motion.div>

            {/* Brands Grid */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={inView ? { opacity: 1 } : {}}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="grid sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-6 sm:gap-8"
            >
              <AnimatePresence>
                {filteredBrands.map((brand, index) => (
                  <motion.div
                    key={brand.id}
                    layout
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className="group cursor-pointer"
                    onClick={() => setSelectedBrand(brand.name)}
                  >
                    <div className="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border border-neutral-100 group-hover:border-accent-200 h-full">
                      {/* Brand Logo */}
                      <div className="relative p-6 sm:p-8 bg-gradient-to-br from-neutral-50 to-white group-hover:from-accent-50 group-hover:to-accent-100 transition-all duration-500">
                        {brand.featured && (
                          <div className="absolute top-4 right-4">
                            <div className="bg-accent-500 text-white px-3 py-1 rounded-full text-xs font-semibold flex items-center space-x-1">
                              <Star className="w-3 h-3" />
                              <span>Featured</span>
                            </div>
                          </div>
                        )}
                        
                        <div className="aspect-square bg-white rounded-xl shadow-sm p-4 group-hover:shadow-md transition-all duration-300">
                          <img
                            src={brand.logo}
                            alt={brand.name}
                            className="w-full h-full object-cover rounded-lg group-hover:scale-105 transition-transform duration-300"
                          />
                        </div>
                      </div>

                      {/* Brand Info */}
                      <div className="p-6 sm:p-8">
                        <h4 className="text-lg sm:text-xl font-heading font-bold text-primary-900 mb-2 group-hover:text-accent-600 transition-colors">
                          {brand.name}
                        </h4>
                        <p className="text-neutral-600 text-sm sm:text-base mb-4 sm:mb-6 leading-relaxed line-clamp-2">
                          {brand.description}
                        </p>

                        {/* CTA */}
                        <div className="flex items-center justify-between">
                          <span className="text-accent-500 font-semibold text-sm group-hover:text-accent-600 transition-colors">
                            View Products
                          </span>
                          <ArrowRight className="w-4 h-4 text-accent-500 group-hover:text-accent-600 group-hover:translate-x-1 transition-all" />
                        </div>
                      </div>

                      {/* Hover Glow Effect */}
                      <div className="absolute inset-0 bg-gradient-to-t from-accent-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none rounded-2xl"></div>
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>
            </motion.div>
          </div>
        </div>

        {/* Brand Detail Modal */}
        <AnimatePresence>
          {selectedBrand && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
              onClick={() => setSelectedBrand(null)}
            >
              <motion.div
                initial={{ opacity: 0, scale: 0.8, y: 50 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.8, y: 50 }}
                className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
                onClick={(e) => e.stopPropagation()}
              >
                {/* Modal Header */}
                <div className="bg-gradient-to-r from-primary-900 to-primary-700 p-6 sm:p-8 text-white">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 sm:w-16 sm:h-16 bg-white rounded-xl p-2">
                        <img
                          src={brands.find(b => b.name === selectedBrand)?.logo}
                          alt={selectedBrand}
                          className="w-full h-full object-cover rounded-lg"
                        />
                      </div>
                      <div>
                        <h3 className="text-xl sm:text-2xl font-heading font-bold">{selectedBrand}</h3>
                        <p className="text-neutral-200 text-sm sm:text-base">
                          {brands.find(b => b.name === selectedBrand)?.description}
                        </p>
                      </div>
                    </div>
                    <button
                      onClick={() => setSelectedBrand(null)}
                      className="p-2 hover:bg-white/20 rounded-lg transition-colors"
                    >
                      <X className="w-6 h-6" />
                    </button>
                  </div>
                </div>

                {/* Modal Content */}
                <div className="p-6 sm:p-8 max-h-96 overflow-y-auto">
                  <h4 className="text-lg sm:text-xl font-heading font-bold text-primary-900 mb-6">
                    Products from {selectedBrand}
                  </h4>
                  
                  <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                    {brandProducts.map((product) => (
                      <div key={product.id} className="bg-neutral-50 rounded-xl p-4 hover:bg-neutral-100 transition-colors">
                        <img
                          src={product.image}
                          alt={product.name}
                          className="w-full h-32 object-cover rounded-lg mb-3"
                        />
                        <h5 className="font-semibold text-primary-900 mb-2 text-sm sm:text-base">{product.name}</h5>
                        <p className="text-sm text-neutral-600 mb-3 line-clamp-2">{product.description}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </section>
  );
};

export default Brands;