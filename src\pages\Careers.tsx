import React, { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { Search, MapPin, Clock, Users, ArrowRight, X, Send, FileText } from 'lucide-react';
import SEO from '../components/common/SEO';
const career2Img = '/images/career 2.png';
import { CheckCircle, Briefcase, Smile, Heart } from 'lucide-react';
import { Helmet } from 'react-helmet-async';

interface Job {
  id: string;
  title: string;
  department: string;
  location: string;
  type: string;
  description: string;
  requirements: string[];
  responsibilities: string[];
  benefits: string[];
}

const jobData = [
  {
    id: 'warehouse',
    title: 'Warehouse Attendant',
    summary: 'Support our logistics team by ensuring efficient inventory management and timely order fulfillment.',
    details: [
      'Assist with receiving, storing, and organizing products.',
      'Prepare and pack orders for delivery.',
      'Maintain a clean and safe warehouse environment.',
      'Collaborate with team members to optimize workflow.'
    ],
    requirements: [
      'High school diploma or equivalent',
      'Attention to detail and reliability',
      'Ability to lift and move products',
      'Team player with a positive attitude'
    ]
  },
  {
    id: 'casual',
    title: 'Casual Worker',
    summary: 'Join our flexible workforce and support various departments as needed, gaining valuable experience.',
    details: [
      'Assist in different areas including warehouse, delivery, and admin.',
      'Adapt to changing tasks and priorities.',
      'Contribute to a positive team environment.'
    ],
    requirements: [
      'Willingness to learn and adapt',
      'Good communication skills',
      'Punctual and dependable',
      'No prior experience required'
    ]
  },
  {
    id: 'sales',
    title: 'Sales Representative',
    summary: 'Drive growth by building relationships with retailers and promoting EverydayValueJamaica products.',
    details: [
      'Engage with retail partners to understand their needs.',
      'Promote and sell our product range.',
      'Meet and exceed sales targets.',
      'Provide feedback to improve our offerings.'
    ],
    requirements: [
      'Strong communication and negotiation skills',
      'Self-motivated and goal-oriented',
      'Valid driver\'s license',
      'Experience in sales is a plus'
    ]
  }
];

const whyJoin = [
  {
    icon: <Briefcase className="w-8 h-8 text-accent-500 mb-2" />,
    title: 'Growth & Development',
    desc: 'Advance your career with training, mentorship, and real opportunities for promotion.'
  },
  {
    icon: <CheckCircle className="w-8 h-8 text-accent-500 mb-2" />,
    title: 'Competitive Benefits',
    desc: 'Enjoy health coverage, bonuses, and flexible arrangements that support your lifestyle.'
  },
  {
    icon: <Smile className="w-8 h-8 text-accent-500 mb-2" />,
    title: 'Team Culture',
    desc: 'Work with passionate professionals who care about making a difference every day.'
  },
  {
    icon: <Heart className="w-8 h-8 text-accent-500 mb-2" />,
    title: 'Work-Life Balance',
    desc: 'We value your time and well-being, offering flexible schedules and support.'
  }
];

const Careers: React.FC = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [selectedDepartment, setSelectedDepartment] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedJob, setSelectedJob] = useState<Job | null>(null);
  const [openJob, setOpenJob] = useState<string | null>(null);
  const [form, setForm] = useState<{ first: string; last: string; email: string; phone: string; resume: File | null }>({ first: '', last: '', email: '', phone: '', resume: null });
  const [formTouched, setFormTouched] = useState<{[k:string]:boolean}>({});
  const [formError, setFormError] = useState<{[k:string]:string}>({});
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [success, setSuccess] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const honeypot = useRef<HTMLInputElement>(null);

  const jobs: Job[] = [
    {
      id: '1',
      title: 'Sales Representative',
      department: 'Sales & Marketing',
      location: 'Kingston, Jamaica',
      type: 'Full-time',
      description: 'Join our dynamic sales team and help expand our market reach across Jamaica. You\'ll be responsible for building relationships with retailers and driving sales growth.',
      requirements: [
        'Bachelor\'s degree in Business, Marketing, or related field',
        '2+ years of sales experience',
        'Excellent communication and interpersonal skills',
        'Valid driver\'s license',
        'Knowledge of Jamaican retail market preferred'
      ],
      responsibilities: [
        'Develop and maintain relationships with retail partners',
        'Achieve monthly and quarterly sales targets',
        'Conduct product presentations and demonstrations',
        'Provide market feedback and insights',
        'Collaborate with marketing team on promotional activities'
      ],
      benefits: [
        'Competitive salary plus commission',
        'Health and dental insurance',
        'Company vehicle',
        'Professional development opportunities',
        'Performance bonuses'
      ]
    },
    {
      id: '2',
      title: 'Warehouse Supervisor',
      department: 'Operations',
      location: 'Kingston, Jamaica',
      type: 'Full-time',
      description: 'Lead our warehouse operations team to ensure efficient inventory management and order fulfillment. This role requires strong leadership and organizational skills.',
      requirements: [
        'High school diploma or equivalent',
        '3+ years of warehouse management experience',
        'Strong leadership and team management skills',
        'Knowledge of inventory management systems',
        'Forklift certification preferred'
      ],
      responsibilities: [
        'Supervise warehouse staff and daily operations',
        'Ensure accurate inventory tracking and management',
        'Coordinate with logistics team for timely deliveries',
        'Implement safety protocols and procedures',
        'Optimize warehouse layout and processes'
      ],
      benefits: [
        'Competitive salary',
        'Health insurance coverage',
        'Paid time off',
        'Training and certification opportunities',
        'Career advancement potential'
      ]
    },
    {
      id: '3',
      title: 'Marketing Coordinator',
      department: 'Sales & Marketing',
      location: 'Kingston, Jamaica',
      type: 'Full-time',
      description: 'Support our marketing initiatives and help develop creative campaigns that drive brand awareness and sales growth across our product portfolio.',
      requirements: [
        'Bachelor\'s degree in Marketing, Communications, or related field',
        '1-2 years of marketing experience',
        'Proficiency in digital marketing tools',
        'Creative thinking and problem-solving skills',
        'Strong written and verbal communication skills'
      ],
      responsibilities: [
        'Develop and execute marketing campaigns',
        'Manage social media accounts and content',
        'Coordinate promotional events and activities',
        'Analyze marketing performance metrics',
        'Support brand management initiatives'
      ],
      benefits: [
        'Competitive starting salary',
        'Health and wellness benefits',
        'Professional development budget',
        'Flexible work arrangements',
        'Creative work environment'
      ]
    },
    {
      id: '4',
      title: 'Delivery Driver',
      department: 'Logistics',
      location: 'Islandwide',
      type: 'Full-time',
      description: 'Join our delivery team and help us maintain our reputation for reliable, timely deliveries across Jamaica. Perfect for someone who enjoys being on the road.',
      requirements: [
        'Valid commercial driver\'s license',
        'Clean driving record',
        'Physical ability to lift and move products',
        'Good knowledge of Jamaican roads and routes',
        'Customer service oriented'
      ],
      responsibilities: [
        'Safely deliver products to retail locations',
        'Maintain delivery schedules and routes',
        'Conduct vehicle inspections and maintenance',
        'Provide excellent customer service',
        'Complete delivery documentation accurately'
      ],
      benefits: [
        'Competitive hourly wage',
        'Overtime opportunities',
        'Health insurance',
        'Fuel allowance',
        'Safety bonuses'
      ]
    }
  ];

  const departments = [
    'all',
    'Sales & Marketing',
    'Operations',
    'Logistics',
    'Human Resources',
    'Finance'
  ];

  const filteredJobs = jobs.filter(job => {
    const matchesDepartment = selectedDepartment === 'all' || job.department === selectedDepartment;
    const matchesSearch = job.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         job.department.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesDepartment && matchesSearch;
  });

  const validate = () => {
    const errors: {[k:string]:string} = {};
    if (!form.first.trim()) errors.first = 'First name is required.';
    if (!form.last.trim()) errors.last = 'Last name is required.';
    if (!form.email.match(/^[^@\s]+@[^@\s]+\.[^@\s]+$/)) errors.email = 'Valid email required.';
    if (!form.phone.match(/^\+?\d{7,15}$/)) errors.phone = 'Valid phone required.';
    if (!form.resume) errors.resume = 'Resume is required.';
    return errors;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, files } = e.target;
    setForm(f => ({ ...f, [name]: files ? files[0] : value }));
    setFormTouched(t => ({ ...t, [name]: true }));
  };

  const handleDrop = (e: React.DragEvent<HTMLLabelElement>) => {
    e.preventDefault();
    setDragActive(false);
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      setForm(f => ({ ...f, resume: e.dataTransfer.files[0] }));
      setFormTouched(t => ({ ...t, resume: true }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormTouched({ first: true, last: true, email: true, phone: true, resume: true });
    setFormError({});
    if (honeypot.current?.value) return;
    const errors = validate();
    if (Object.keys(errors).length) {
      setFormError(errors);
      return;
    }
    setUploading(true);
    setUploadProgress(0);
    for (let i = 1; i <= 10; i++) {
      await new Promise(res => setTimeout(res, 80));
      setUploadProgress(i * 10);
    }
    setUploading(false);
    setSuccess(true);
    setForm({ first: '', last: '', email: '', phone: '', resume: null });
    setTimeout(() => setSuccess(false), 4000);
  };

  return (
    <>
      <SEO
        title="Careers - Join the Everyday Value Jamaica Team"
        description="Explore exciting career opportunities at Everyday Value Jamaica. Make a difference every day with Jamaica's premier distribution company. Apply now!"
        keywords="careers, jobs, employment, Jamaica, distribution, Everyday Value Jamaica, opportunities, hiring"
        image={career2Img}
      />
      <Helmet>
        <meta name="robots" content="index, follow" />
      </Helmet>
      <div className="pt-20">
        <section className="w-full min-h-[480px] bg-gradient-to-br from-primary-900 via-primary-700 to-accent-500 flex flex-col md:flex-row items-stretch overflow-hidden relative">
          <div className="md:w-1/2 w-full h-64 md:h-auto relative">
            <img src={career2Img} alt="EverydayValueJamaica team collaborating in office" className="object-cover w-full h-full md:rounded-r-[3rem] shadow-xl" loading="eager" />
          </div>
          <div className="md:w-1/2 w-full flex flex-col justify-center px-6 sm:px-12 py-12 md:py-0">
            <motion.h1
              initial={{ opacity: 0, x: 60 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="text-2xl sm:text-3xl md:text-4xl font-heading font-bold mb-6 text-white drop-shadow-lg"
            >
              Join the Everyday<b>Val<span className="text-yellow-400">U</span>e</b>Jamaica Team —<br className="hidden md:block" /> Make a Difference, Every Day.
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 0.3 }}
              className="text-lg sm:text-xl text-white/90 max-w-xl font-medium mb-4"
            >
              At Everyday Value Jamaica, you're not just starting a job — you're joining a family that values impact, teamwork, and growth. Discover opportunities to build your future and help shape Jamaica's success story.
            </motion.p>
          </div>
        </section>

        <section className="bg-white py-16 sm:py-20 lg:py-24">
          <div className="container mx-auto px-6 sm:px-8">
            <h2 className="text-xl sm:text-2xl font-heading font-bold text-primary-900 mb-10 text-center">Career Opportunities</h2>
            <div className="grid md:grid-cols-3 gap-8">
              {jobData.map(job => (
                <motion.div
                  key={job.id}
                  whileHover={{ y: -8, boxShadow: '0 8px 32px 0 rgba(56,189,248,0.15)' }}
                  className={`rounded-2xl border border-neutral-100 bg-white shadow-md transition-all duration-300 flex flex-col ${openJob === job.id ? 'ring-2 ring-accent-400' : ''}`}
                  tabIndex={0}
                  aria-label={`Expand job card: ${job.title}`}
                  onClick={() => setOpenJob(openJob === job.id ? null : job.id)}
                  onKeyDown={e => { if (e.key === 'Enter' || e.key === ' ') setOpenJob(openJob === job.id ? null : job.id); }}
                >
                  <div className="p-6 flex-1 flex flex-col">
                    <h3 className="text-xl font-bold text-accent-700 mb-2">{job.title}</h3>
                    <p className="text-neutral-700 mb-4 flex-1">{job.summary}</p>
                    <button
                      className="mt-auto bg-accent-500 hover:bg-accent-600 text-white px-5 py-2 rounded-lg font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-accent-400"
                      aria-label={`View details for ${job.title}`}
                    >
                      View Details
                    </button>
                  </div>
                  <AnimatePresence>
                    {openJob === job.id && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.4 }}
                        className="px-6 pb-6"
                      >
                        <div className="mt-2">
                          <h4 className="font-bold text-primary-900 mb-2">Key Responsibilities</h4>
                          <ul className="list-disc pl-5 text-neutral-700 mb-4">
                            {job.details.map((d, i) => <li key={i}>{d}</li>)}
                          </ul>
                          <h4 className="font-bold text-primary-900 mb-2">Requirements</h4>
                          <ul className="list-disc pl-5 text-neutral-700">
                            {job.requirements.map((r, i) => <li key={i}>{r}</li>)}
                          </ul>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        <section className="bg-neutral-50 py-16 sm:py-20 lg:py-24">
          <div className="container mx-auto px-6 sm:px-8 max-w-2xl">
            <h2 className="text-xl sm:text-2xl font-heading font-bold text-primary-900 mb-8 text-center">Apply Now</h2>
            <form className="space-y-8" onSubmit={handleSubmit} autoComplete="off" aria-label="Job Application Form">
              <input type="text" name="website" ref={honeypot} className="hidden" tabIndex={-1} autoComplete="off" aria-hidden="true" />
              <div className="grid sm:grid-cols-2 gap-6">
                <div className="relative">
                  <input
                    type="text"
                    name="first"
                    value={form.first}
                    onChange={handleChange}
                    onBlur={() => setFormTouched(t => ({ ...t, first: true }))}
                    className={`peer w-full px-4 pt-6 pb-2 border ${formTouched.first && formError.first ? 'border-red-400' : 'border-neutral-200'} rounded-lg bg-white focus:ring-2 focus:ring-accent-400 focus:border-transparent transition-all`}
                    aria-label="First Name"
                    aria-invalid={!!formError.first}
                  />
                  <label className="absolute left-4 top-2 text-neutral-400 text-sm transition-all peer-focus:-translate-y-3 peer-focus:scale-90 peer-focus:text-accent-500 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:scale-100 pointer-events-none">First Name</label>
                  {formTouched.first && formError.first && <span className="text-red-500 text-xs mt-1 block">{formError.first}</span>}
                </div>
                <div className="relative">
                  <input
                    type="text"
                    name="last"
                    value={form.last}
                    onChange={handleChange}
                    onBlur={() => setFormTouched(t => ({ ...t, last: true }))}
                    className={`peer w-full px-4 pt-6 pb-2 border ${formTouched.last && formError.last ? 'border-red-400' : 'border-neutral-200'} rounded-lg bg-white focus:ring-2 focus:ring-accent-400 focus:border-transparent transition-all`}
                    aria-label="Last Name"
                    aria-invalid={!!formError.last}
                  />
                  <label className="absolute left-4 top-2 text-neutral-400 text-sm transition-all peer-focus:-translate-y-3 peer-focus:scale-90 peer-focus:text-accent-500 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:scale-100 pointer-events-none">Last Name</label>
                  {formTouched.last && formError.last && <span className="text-red-500 text-xs mt-1 block">{formError.last}</span>}
                </div>
              </div>
              <div className="relative">
                <input
                  type="email"
                  name="email"
                  value={form.email}
                  onChange={handleChange}
                  onBlur={() => setFormTouched(t => ({ ...t, email: true }))}
                  className={`peer w-full px-4 pt-6 pb-2 border ${formTouched.email && formError.email ? 'border-red-400' : 'border-neutral-200'} rounded-lg bg-white focus:ring-2 focus:ring-accent-400 focus:border-transparent transition-all`}
                  aria-label="Email"
                  aria-invalid={!!formError.email}
                />
                <label className="absolute left-4 top-2 text-neutral-400 text-sm transition-all peer-focus:-translate-y-3 peer-focus:scale-90 peer-focus:text-accent-500 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:scale-100 pointer-events-none">Email</label>
                {formTouched.email && formError.email && <span className="text-red-500 text-xs mt-1 block">{formError.email}</span>}
              </div>
              <div className="relative">
                <input
                  type="tel"
                  name="phone"
                  value={form.phone}
                  onChange={handleChange}
                  onBlur={() => setFormTouched(t => ({ ...t, phone: true }))}
                  className={`peer w-full px-4 pt-6 pb-2 border ${formTouched.phone && formError.phone ? 'border-red-400' : 'border-neutral-200'} rounded-lg bg-white focus:ring-2 focus:ring-accent-400 focus:border-transparent transition-all`}
                  aria-label="Phone Number"
                  aria-invalid={!!formError.phone}
                />
                <label className="absolute left-4 top-2 text-neutral-400 text-sm transition-all peer-focus:-translate-y-3 peer-focus:scale-90 peer-focus:text-accent-500 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:scale-100 pointer-events-none">Phone Number</label>
                {formTouched.phone && formError.phone && <span className="text-red-500 text-xs mt-1 block">{formError.phone}</span>}
              </div>
              <div className="relative">
                <label
                  htmlFor="resume"
                  className={`flex flex-col items-center justify-center border-2 ${dragActive ? 'border-accent-500 bg-accent-50' : formTouched.resume && formError.resume ? 'border-red-400' : 'border-dashed border-neutral-300'} rounded-lg py-8 cursor-pointer transition-all text-neutral-500 hover:border-accent-400 hover:bg-accent-50 focus-within:border-accent-400 focus-within:bg-accent-50`}
                  onDragOver={e => { e.preventDefault(); setDragActive(true); }}
                  onDragLeave={e => { e.preventDefault(); setDragActive(false); }}
                  onDrop={handleDrop}
                  tabIndex={0}
                  aria-label="Resume Upload"
                >
                  <FileText className="w-8 h-8 mb-2" />
                  <span className="mb-2">Drag & drop your resume here, or <span className="underline">browse</span></span>
                  <input
                    id="resume"
                    name="resume"
                    type="file"
                    accept=".pdf,.doc,.docx"
                    className="hidden"
                    onChange={handleChange}
                    onBlur={() => setFormTouched(t => ({ ...t, resume: true }))}
                    aria-label="Resume Upload"
                  />
                  {form.resume && <span className="text-accent-700 font-semibold mt-2">{(form.resume as File).name}</span>}
                </label>
                {formTouched.resume && formError.resume && <span className="text-red-500 text-xs mt-1 block">{formError.resume}</span>}
                {uploading && (
                  <div className="w-full bg-neutral-200 rounded-full h-2 mt-3 overflow-hidden">
                    <motion.div
                      initial={{ width: 0 }}
                      animate={{ width: `${uploadProgress}%` }}
                      transition={{ duration: 0.5 }}
                      className="bg-accent-500 h-2 rounded-full"
                    />
                  </div>
                )}
              </div>
              <button
                type="submit"
                className="w-full bg-accent-500 hover:bg-accent-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors flex items-center justify-center gap-2 disabled:opacity-60 disabled:cursor-not-allowed"
                disabled={uploading}
                aria-label="Submit Application"
              >
                {uploading ? 'Submitting...' : <><Send className="w-5 h-5" /> Submit Application</>}
              </button>
              <p className="text-xs text-neutral-500 text-center mt-2">Your information will be kept confidential and used solely for recruitment purposes.</p>
              <AnimatePresence>
                {success && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 10 }}
                    className="text-green-600 text-center font-semibold mt-4"
                    role="status"
                    aria-live="polite"
                  >
                    Thank you for applying! We'll be in touch if your profile matches our needs.
                  </motion.div>
                )}
              </AnimatePresence>
            </form>
          </div>
        </section>

        <div className="w-full h-20 bg-gradient-to-tr from-primary-900 via-accent-500 to-accent-400" style={{ clipPath: 'polygon(0 100%, 100% 0, 100% 100%, 0 100%)' }} aria-hidden="true"></div>

        <section className="bg-white py-16 sm:py-20 lg:py-24">
          <div className="container mx-auto px-6 sm:px-8">
            <h2 className="text-xl sm:text-2xl font-heading font-bold text-primary-900 mb-10 text-center">Why Join Everyday Value Jamaica?</h2>
            <div className="grid sm:grid-cols-2 md:grid-cols-4 gap-8">
              {whyJoin.map((item, i) => (
                <motion.div
                  key={item.title}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true, amount: 0.5 }}
                  transition={{ duration: 0.6, delay: i * 0.1 }}
                  className="bg-accent-50 rounded-2xl shadow-md p-8 flex flex-col items-center text-center hover:shadow-xl transition-all cursor-pointer"
                  tabIndex={0}
                  aria-label={item.title}
                >
                  {item.icon}
                  <h3 className="text-lg font-bold text-accent-700 mb-2">{item.title}</h3>
                  <p className="text-neutral-700">{item.desc}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>
      </div>
    </>
  );
};

export default Careers;