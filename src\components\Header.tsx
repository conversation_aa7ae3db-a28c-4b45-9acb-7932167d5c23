import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Menu, X, Phone, Mail } from 'lucide-react';

interface HeaderProps {
  activeSection: string;
  onNavigate: (section: string) => void;
}

const Header: React.FC<HeaderProps> = ({ activeSection, onNavigate }) => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navItems = [
    { id: 'hero', label: 'Home' },
    { id: 'about', label: 'About' },
    { id: 'services', label: 'Services' },
    { id: 'brands', label: 'Brands' },
    { id: 'products', label: 'Products' },
    { id: 'promotions', label: 'Promotions' },
    { id: 'careers', label: 'Careers' },
    { id: 'news', label: 'News' },
    { id: 'contact', label: 'Contact' }
  ];

  return (
    <>
      <motion.header
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
          isScrolled 
            ? 'bg-white/95 backdrop-blur-md shadow-lg' 
            : 'bg-transparent'
        }`}
      >
        <div className="container mx-auto px-6 sm:px-8">
          <div className="flex items-center justify-between h-20">
            {/* Logo */}
            <motion.div 
              className="flex items-center space-x-3"
              whileHover={{ scale: 1.05 }}
            >
              <img
                src="/images/everyday_logo_sm.png"
                alt="EverydayValueJamaica"
                className="h-10 sm:h-12 w-auto"
              />
              <div className="hidden sm:block">
                <h1 className={`text-lg sm:text-xl font-heading font-bold ${
                  isScrolled ? 'text-primary-900' : 'text-white'
                }`}>
                  EverydayValueJamaica
                </h1>
                <p className={`text-xs ${
                  isScrolled ? 'text-neutral-600' : 'text-neutral-200'
                }`}>
                  Best Brands. Best Price. Best Choice.
                </p>
              </div>
            </motion.div>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-6 xl:space-x-8">
              {navItems.map((item) => (
                <motion.button
                  key={item.id}
                  onClick={() => onNavigate(item.id)}
                  className={`text-sm font-medium transition-colors duration-200 ${
                    activeSection === item.id
                      ? isScrolled 
                        ? 'text-accent-500' 
                        : 'text-accent-400'
                      : isScrolled 
                        ? 'text-neutral-700 hover:text-accent-500' 
                        : 'text-white hover:text-accent-400'
                  }`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {item.label}
                </motion.button>
              ))}
            </nav>

            {/* Contact Info & Mobile Menu */}
            <div className="flex items-center space-x-4">
              <div className="hidden xl:flex items-center space-x-4">
                <a 
                  href="tel:+1************" 
                  className={`flex items-center space-x-2 text-sm ${
                    isScrolled ? 'text-neutral-700' : 'text-white'
                  }`}
                >
                  <Phone size={16} />
                  <span>************</span>
                </a>
                <a 
                  href="mailto:<EMAIL>"
                  className={`flex items-center space-x-2 text-sm ${
                    isScrolled ? 'text-neutral-700' : 'text-white'
                  }`}
                >
                  <Mail size={16} />
                  <span><EMAIL></span>
                </a>
              </div>

              {/* Mobile Menu Button */}
              <button
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className={`lg:hidden p-2 rounded-md ${
                  isScrolled ? 'text-neutral-700' : 'text-white'
                }`}
              >
                {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Menu */}
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ 
            opacity: isMobileMenuOpen ? 1 : 0, 
            height: isMobileMenuOpen ? 'auto' : 0 
          }}
          className="lg:hidden bg-white border-t border-neutral-200 overflow-hidden"
        >
          <div className="container mx-auto px-6 sm:px-8 py-4">
            <nav className="flex flex-col space-y-4">
              {navItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => {
                    onNavigate(item.id);
                    setIsMobileMenuOpen(false);
                  }}
                  className={`text-left text-neutral-700 hover:text-accent-500 transition-colors text-base ${
                    activeSection === item.id ? 'text-accent-500 font-medium' : ''
                  }`}
                >
                  {item.label}
                </button>
              ))}
            </nav>
            
            {/* Mobile Contact Info */}
            <div className="mt-6 pt-4 border-t border-neutral-200 space-y-3">
              <a 
                href="tel:+1************" 
                className="flex items-center space-x-2 text-neutral-700 text-sm"
              >
                <Phone size={16} />
                <span>************</span>
              </a>
              <a
                href="mailto:<EMAIL>"
                className="flex items-center space-x-2 text-neutral-700 text-sm"
              >
                <Mail size={16} />
                <span><EMAIL></span>
              </a>
            </div>
          </div>
        </motion.div>
      </motion.header>
    </>
  );
};

export default Header;