import { useState, useMemo, useCallback } from 'react';
import Fuse from 'fuse.js';
import { products, brands, categories } from '../data/brands';

export interface SearchResult {
  type: 'product' | 'brand' | 'category';
  item: any;
  score?: number;
  matches?: any[];
}

export interface SearchSuggestion {
  id: string;
  text: string;
  type: 'product' | 'brand' | 'category';
  image?: string;
  brand?: string;
  category?: string;
  action: () => void;
}

const useSearch = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isSearchOpen, setIsSearchOpen] = useState(false);

  // Configure Fuse.js for fuzzy search
  const productFuse = useMemo(() => new Fuse(products, {
    keys: [
      { name: 'name', weight: 0.7 },
      { name: 'brand', weight: 0.5 },
      { name: 'description', weight: 0.3 },
      { name: 'tags', weight: 0.4 }
    ],
    threshold: 0.4,
    includeScore: true,
    includeMatches: true,
    minMatchCharLength: 2
  }), []);

  const brandFuse = useMemo(() => new Fuse(brands, {
    keys: [
      { name: 'name', weight: 0.8 },
      { name: 'description', weight: 0.4 }
    ],
    threshold: 0.3,
    includeScore: true,
    includeMatches: true,
    minMatchCharLength: 2
  }), []);

  const categoryFuse = useMemo(() => new Fuse(categories.slice(1), {
    keys: [
      { name: 'name', weight: 1.0 }
    ],
    threshold: 0.3,
    includeScore: true,
    includeMatches: true,
    minMatchCharLength: 2
  }), []);

  // Search function
  const search = useCallback((query: string): SearchResult[] => {
    if (!query || query.length < 2) return [];

    const productResults = productFuse.search(query).map(result => ({
      type: 'product' as const,
      item: result.item,
      score: result.score,
      matches: result.matches
    }));

    const brandResults = brandFuse.search(query).map(result => ({
      type: 'brand' as const,
      item: result.item,
      score: result.score,
      matches: result.matches
    }));

    const categoryResults = categoryFuse.search(query).map(result => ({
      type: 'category' as const,
      item: result.item,
      score: result.score,
      matches: result.matches
    }));

    // Combine and sort by score
    const allResults = [...productResults, ...brandResults, ...categoryResults];
    return allResults.sort((a, b) => (a.score || 0) - (b.score || 0)).slice(0, 8);
  }, [productFuse, brandFuse, categoryFuse]);

  // Generate suggestions for autocomplete
  const suggestions = useMemo((): SearchSuggestion[] => {
    const results = search(searchTerm);
    
    return results.map(result => {
      switch (result.type) {
        case 'product':
          return {
            id: result.item.id,
            text: result.item.name,
            type: 'product',
            image: result.item.image,
            brand: result.item.brand,
            category: categories.find(c => c.id === result.item.category)?.name,
            action: () => {
              // Scroll to product or open modal
              const element = document.getElementById(`product-${result.item.id}`);
              if (element) {
                element.scrollIntoView({ behavior: 'smooth', block: 'center' });
              } else {
                // Navigate to products page with filter
                window.location.href = `/products?search=${encodeURIComponent(result.item.name)}`;
              }
              setIsSearchOpen(false);
              setSearchTerm('');
            }
          };
        case 'brand':
          return {
            id: result.item.id,
            text: result.item.name,
            type: 'brand',
            image: result.item.logo,
            action: () => {
              // Navigate to brands page or scroll to brand section
              window.location.href = `/brands?brand=${encodeURIComponent(result.item.name)}`;
              setIsSearchOpen(false);
              setSearchTerm('');
            }
          };
        case 'category':
          return {
            id: result.item.id,
            text: result.item.name,
            type: 'category',
            action: () => {
              // Navigate to products page with category filter
              window.location.href = `/products?category=${result.item.id}`;
              setIsSearchOpen(false);
              setSearchTerm('');
            }
          };
        default:
          return {
            id: '',
            text: '',
            type: 'product',
            action: () => {}
          };
      }
    });
  }, [searchTerm, search]);

  return {
    searchTerm,
    setSearchTerm,
    isSearchOpen,
    setIsSearchOpen,
    suggestions,
    search
  };
};

export default useSearch;