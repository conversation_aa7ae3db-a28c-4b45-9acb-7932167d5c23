import React, { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { 
  Phone, 
  Mail, 
  MapPin, 
  Clock, 
  Send, 
  MessageSquare,
  User,
  Building,
  CheckCircle,
  Info,
  Users,
  Headphones,
  Briefcase,
  HelpCircle
} from 'lucide-react';
import SEO from '../components/common/SEO';

const gradientAnimation = {
  background: [
    'linear-gradient(120deg, #1e3a8a 0%, #2563eb 50%, #38bdf8 100%)',
    'linear-gradient(120deg, #2563eb 0%, #38bdf8 50%, #1e3a8a 100%)',
    'linear-gradient(120deg, #1e3a8a 0%, #2563eb 50%, #38bdf8 100%)',
  ],
  transition: {
    duration: 10,
    repeat: Infinity,
    ease: 'linear',
  },
};

// Brand color for 'U' in 'You'
const brandColor = '#fbbf24'; // Everyday Value's accent (adjust if needed)

const Contact: React.FC = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    phone: '',
    subject: '',
    message: ''
  });

  const [isSubmitted, setIsSubmitted] = useState(false);

  const heroRef = useRef<HTMLDivElement>(null);

  const openWhatsApp = () => {
    const whatsappNumber = "18764331359";
    const message = encodeURIComponent("Hi! I'd like to get in touch with EverydayValueJamaica regarding your products and services.");
    const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${message}`;
    window.open(whatsappUrl, '_blank');
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Here you would typically send the form data to your backend
    console.log('Form submitted:', formData);
    setIsSubmitted(true);
    
    // Reset form after 3 seconds
    setTimeout(() => {
      setIsSubmitted(false);
      setFormData({
        name: '',
        email: '',
        company: '',
        phone: '',
        subject: '',
        message: ''
      });
    }, 3000);
  };

  const contactInfo = [
    {
      icon: Phone,
      title: 'Phone Numbers',
      details: [
        '(*************',
        '(*************',
        'Fax: (*************'
      ],
      action: 'tel:+1876-433-1359'
    },
    {
      icon: Mail,
      title: 'Email Address',
      details: ['<EMAIL>'],
      action: 'mailto:<EMAIL>'
    },
    {
      icon: MapPin,
      title: 'Office Location',
      details: [
        '12 Colbeck Avenue',
        'Kingston 10',
        'Jamaica W. I.'
      ],
      action: 'https://maps.google.com/?q=12+Colbeck+Avenue+Kingston+10+Jamaica'
    }
  ];

  const officeHours = {
    hours: 'Monday - Friday: 09:00 AM to 05:00 PM',
    weekend: 'Saturday - Sunday: CLOSED'
  };

  // --- Contact Method Cards Data ---
  const contactCards = [
    {
      key: 'phone',
      label: 'Phone',
      icon: Phone,
      value: '(*************',
      action: () => window.open('tel:+18764331359'),
      tooltip: 'Call us now',
      color: 'from-primary-600 to-primary-500',
    },
    {
      key: 'email',
      label: 'Email',
      icon: Mail,
      value: '<EMAIL>',
      action: () => window.open('mailto:<EMAIL>'),
      tooltip: 'Send us an email',
      color: 'from-primary-600 to-primary-500',
    },
    {
      key: 'location',
      label: 'Location',
      icon: MapPin,
      value: '12 Colbeck Avenue, Kingston 10',
      action: () => window.open('https://maps.google.com/?q=12+Colbeck+Avenue+Kingston+10+Jamaica', '_blank'),
      tooltip: 'View on Google Maps',
      color: 'from-primary-700 to-primary-600',
    },
    {
      key: 'whatsapp',
      label: 'WhatsApp',
      icon: MessageSquare,
      value: '(*************',
      action: openWhatsApp,
      tooltip: 'Chat with us on WhatsApp',
      color: 'from-green-600 to-green-500',
    },
  ];

  // --- Subject Selector Data ---
  const subjectOptions = [
    { value: 'sales', label: 'Sales', icon: Users, color: 'text-blue-500' },
    { value: 'hr', label: 'HR', icon: Briefcase, color: 'text-green-500' },
    { value: 'support', label: 'Support', icon: Headphones, color: 'text-accent-500' },
    { value: 'other', label: 'Other', icon: Info, color: 'text-neutral-500' },
  ];

  // --- Helper for live status ---
  function getDepartmentStatus(dept: string) {
    const now = new Date();
    const hour = now.getHours();
    // 9am-5pm Mon-Fri is online
    const isWeekday = now.getDay() >= 1 && now.getDay() <= 5;
    if (isWeekday && hour >= 9 && hour < 17) return 'online';
    return 'offline';
  }

  const [formErrors, setFormErrors] = useState<any>({});
  const [formTouched, setFormTouched] = useState<any>({});
  const [formFocus, setFormFocus] = useState<string | null>(null);
  const [selectedSubject, setSelectedSubject] = useState(subjectOptions[0].value);

  // --- Office Modal State ---
  const [isMapModalOpen, setIsMapModalOpen] = useState(false);

  const [helpOpen, setHelpOpen] = useState(false);

  function validateField(name: string, value: string) {
    switch (name) {
      case 'name':
        return value.length < 2 ? 'Enter your full name' : '';
      case 'email':
        return !/^\S+@\S+\.\S+$/.test(value) ? 'Enter a valid email' : '';
      case 'message':
        return value.length < 10 ? 'Message too short' : '';
      default:
        return '';
    }
  }

  function handleSmartInputChange(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    setFormErrors((prev: any) => ({ ...prev, [name]: validateField(name, value) }));
  }
  function handleSmartBlur(e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) {
    const { name, value } = e.target;
    setFormTouched((prev: any) => ({ ...prev, [name]: true }));
    setFormErrors((prev: any) => ({ ...prev, [name]: validateField(name, value) }));
    setFormFocus(null);
  }
  function handleSmartFocus(e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) {
    setFormFocus(e.target.name);
  }
  function handleSubjectSelect(val: string) {
    setSelectedSubject(val);
    setFormData((prev) => ({ ...prev, subject: val }));
  }

  return (
    <>
      <SEO
        title="Contact Us - EverydayValueJamaica"
        description="Get in touch with EverydayValueJamaica. Contact our team for distribution services, partnerships, or general inquiries. We're here to help your business grow."
        keywords="contact, EverydayValueJamaica, Jamaica, distribution, phone, email, address, Kingston, customer service, WhatsApp"
      />

      {/* Dynamic Hero Header */}
      <motion.section
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1 }}
        className="relative w-full min-h-[40vh] flex items-end justify-center overflow-hidden"
        style={{ minHeight: '320px' }}
      >
        {/* Full-width branded image */}
        <img
          src="/images/contact us.png"
          alt="Contact Us Banner"
          className="absolute inset-0 w-full h-full object-cover object-center z-0"
          style={{ minHeight: '320px', maxHeight: '480px' }}
        />
        {/* Soft overlay gradient for readability */}
        <div className="absolute inset-0 z-10 bg-gradient-to-b from-black/40 via-black/10 to-white/0" />
        {/* Centered headline and subheading */}
        <div className="relative z-20 w-full max-w-3xl mx-auto text-center py-16 sm:py-24 flex flex-col items-center justify-center">
          <h1 className="text-3xl sm:text-5xl md:text-6xl font-heading font-bold text-white drop-shadow-lg mb-4">
            We'd Love to Hear from Yo
            <span style={{ color: brandColor }}>U</span>
          </h1>
        </div>
        {/* Soft divider/scroll fade to next section */}
        <div className="absolute bottom-0 left-0 w-full h-16 bg-gradient-to-b from-transparent to-white z-30 pointer-events-none" />
      </motion.section>

      {/* Floating Contact Widget */}
      <div className="fixed bottom-24 right-8 z-50">
        <button
          onClick={() => setHelpOpen((v) => !v)}
          className="bg-accent-500 hover:bg-accent-600 text-white rounded-full shadow-xl w-16 h-16 flex items-center justify-center text-3xl transition-all focus:outline-none focus:ring-4 focus:ring-accent-300 animate-pulse-glow"
          aria-label="Need Help?"
        >
          <HelpCircle className="w-8 h-8" />
        </button>
        {helpOpen && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            className="absolute bottom-20 right-0 bg-white rounded-2xl shadow-2xl p-6 flex flex-col gap-4 min-w-[260px] border border-accent-100"
          >
            <div className="font-bold text-primary-900 mb-2">Quick Contact</div>
            {contactCards.map(card => (
              <button
                key={card.key}
                onClick={card.action}
                className={`flex items-center gap-3 px-4 py-2 rounded-xl bg-gradient-to-r ${card.color} text-white shadow-md hover:shadow-lg transition-all duration-300 group`}
              >
                <card.icon className="w-5 h-5 group-hover:scale-125 transition-transform duration-300" />
                <span className="font-semibold text-base">{card.label}</span>
                <span className="ml-auto text-xs opacity-80">{card.value}</span>
              </button>
            ))}
          </motion.div>
        )}
      </div>

      {/* Responsive Grid Layout for Main Content */}
      <div className="w-full max-w-7xl mx-auto px-4 sm:px-8 grid grid-cols-1 lg:grid-cols-3 gap-12 py-16">
        {/* Main Grid Contact Cards */}
        <div className="col-span-1 flex flex-col gap-8">
          {contactCards.map(card => (
            <motion.button
              key={card.key}
              onClick={card.action}
              whileHover={{ scale: 1.04, boxShadow: '0 4px 32px 0 rgba(56,189,248,0.15)' }}
              className={`flex items-center gap-4 px-6 py-6 rounded-2xl bg-gradient-to-r ${card.color} text-white shadow-lg hover:shadow-2xl transition-all duration-300 group relative`}
            >
              <card.icon className="w-8 h-8 group-hover:scale-125 transition-transform duration-300" />
              <div className="flex flex-col text-left">
                <span className="font-bold text-lg">{card.label}</span>
                <span className="text-sm opacity-80">{card.value}</span>
              </div>
              <span className="absolute left-1/2 -bottom-8 -translate-x-1/2 bg-black/80 text-white text-xs rounded px-2 py-1 opacity-0 group-hover:opacity-100 transition-all pointer-events-none whitespace-nowrap z-50">{card.tooltip}</span>
            </motion.button>
          ))}
        </div>
        {/* Smart Contact Form and Map */}
        <div className="col-span-2 flex flex-col gap-12">
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="bg-white/90 rounded-2xl shadow-2xl p-8"
          >
            <h2 className="text-2xl sm:text-3xl font-heading font-bold text-primary-900 mb-6">
              Send Us a Message
            </h2>
            <form onSubmit={handleSubmit} className="space-y-8">
              <div className="grid sm:grid-cols-2 gap-8">
                {/* Name */}
                <div className="relative">
                  <label className="block text-sm font-semibold text-neutral-700 mb-2">Full Name *</label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleSmartInputChange}
                    onBlur={handleSmartBlur}
                    onFocus={handleSmartFocus}
                    required
                    className={`w-full px-4 py-3 border rounded-lg transition-all focus:ring-2 focus:ring-accent-500 focus:border-transparent ${formFocus==='name' ? 'scale-105 shadow-lg' : ''} ${formErrors.name && formTouched.name ? 'border-red-400' : 'border-neutral-200'}`}
                    placeholder="e.g. Jane Doe"
                  />
                  <span className="text-xs text-neutral-400 mt-1 block">Enter your full name</span>
                  {formErrors.name && formTouched.name && (
                    <span className="text-xs text-red-500 mt-1 block">{formErrors.name}</span>
                  )}
                </div>
                {/* Email */}
                <div className="relative">
                  <label className="block text-sm font-semibold text-neutral-700 mb-2">Email Address *</label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleSmartInputChange}
                    onBlur={handleSmartBlur}
                    onFocus={handleSmartFocus}
                    required
                    className={`w-full px-4 py-3 border rounded-lg transition-all focus:ring-2 focus:ring-accent-500 focus:border-transparent ${formFocus==='email' ? 'scale-105 shadow-lg' : ''} ${formErrors.email && formTouched.email ? 'border-red-400' : 'border-neutral-200'}`}
                    placeholder="e.g. <EMAIL>"
                  />
                  <span className="text-xs text-neutral-400 mt-1 block">We'll never share your email.</span>
                  {formErrors.email && formTouched.email && (
                    <span className="text-xs text-red-500 mt-1 block">{formErrors.email}</span>
                  )}
                </div>
              </div>
              {/* Company */}
              <div className="relative">
                <label className="block text-sm font-semibold text-neutral-700 mb-2">Company Name</label>
                <input
                  type="text"
                  name="company"
                  value={formData.company}
                  onChange={handleSmartInputChange}
                  onBlur={handleSmartBlur}
                  onFocus={handleSmartFocus}
                  className={`w-full px-4 py-3 border rounded-lg transition-all focus:ring-2 focus:ring-accent-500 focus:border-transparent ${formFocus==='company' ? 'scale-105 shadow-lg' : ''} border-neutral-200`}
                  placeholder="e.g. Everyday Value Ltd."
                />
                <span className="text-xs text-neutral-400 mt-1 block">Optional: Your company or organization</span>
              </div>
              {/* Phone */}
              <div className="relative">
                <label className="block text-sm font-semibold text-neutral-700 mb-2">Phone Number</label>
                <input
                  type="tel"
                  name="phone"
                  value={formData.phone}
                  onChange={handleSmartInputChange}
                  onBlur={handleSmartBlur}
                  onFocus={handleSmartFocus}
                  className={`w-full px-4 py-3 border rounded-lg transition-all focus:ring-2 focus:ring-accent-500 focus:border-transparent ${formFocus==='phone' ? 'scale-105 shadow-lg' : ''} border-neutral-200`}
                  placeholder="e.g. (*************"
                />
                <span className="text-xs text-neutral-400 mt-1 block">Optional: For a callback</span>
              </div>
              {/* Floating Subject Selector */}
              <div className="relative">
                <label className="block text-sm font-semibold text-neutral-700 mb-2">Subject *</label>
                <div className="flex gap-3">
                  {subjectOptions.map(opt => (
                    <button
                      type="button"
                      key={opt.value}
                      onClick={() => handleSubjectSelect(opt.value)}
                      className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-all font-semibold text-sm ${selectedSubject===opt.value ? 'bg-primary-500 text-white shadow-lg' : 'bg-neutral-100 text-neutral-700 hover:bg-primary-100'} ${opt.color}`}
                    >
                      <opt.icon className="w-4 h-4" /> {opt.label}
                    </button>
                  ))}
                </div>
                <span className="text-xs text-neutral-400 mt-1 block">Choose the department for your inquiry</span>
              </div>
              {/* Message */}
              <div className="relative">
                <label className="block text-sm font-semibold text-neutral-700 mb-2">Message *</label>
                <textarea
                  name="message"
                  value={formData.message}
                  onChange={handleSmartInputChange}
                  onBlur={handleSmartBlur}
                  onFocus={handleSmartFocus}
                  required
                  rows={6}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-transparent transition-all resize-none ${formFocus==='message' ? 'scale-105 shadow-lg' : ''} ${formErrors.message && formTouched.message ? 'border-red-400' : 'border-neutral-200'}`}
                  placeholder="Tell us about your inquiry or how we can help you..."
                />
                <span className="text-xs text-neutral-400 mt-1 block">Be as detailed as possible</span>
                {formErrors.message && formTouched.message && (
                  <span className="text-xs text-red-500 mt-1 block">{formErrors.message}</span>
                )}
              </div>
              <motion.button
                type="submit"
                className="w-full bg-accent-500 hover:bg-accent-600 text-white py-4 rounded-lg font-semibold transition-all duration-300 flex items-center justify-center space-x-2 text-lg shadow-lg"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                disabled={Object.values(formErrors).some(Boolean)}
              >
                <Send className="w-5 h-5" />
                <span>Send Message</span>
              </motion.button>
            </form>
          </motion.div>
          {/* Google Map and Office Hours (scaffold) */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="flex flex-col lg:flex-row gap-8"
          >
            {/* Interactive Google Map */}
            <div className="flex-1 min-h-[300px] bg-neutral-100 rounded-2xl flex items-center justify-center shadow-lg relative overflow-hidden cursor-pointer group"
              onClick={() => setIsMapModalOpen(true)}
              title="Click to view office info and directions"
            >
              {/* Google Maps Embed (replace src with your real map if needed) */}
              <iframe
                title="Office Location"
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3794.002624760368!2d-76.8095!3d18.0123!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x8edb3f7e2e2e2e2e%3A0x1234567890abcdef!2s12%20Colbeck%20Ave%2C%20Kingston%2C%20Jamaica!5e0!3m2!1sen!2sjm!4v1680000000000!5m2!1sen!2sjm"
                width="100%"
                height="100%"
                style={{ border: 0, minHeight: 300, borderRadius: '1rem', filter: 'grayscale(0.2)' }}
                allowFullScreen
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
                className="absolute inset-0 w-full h-full z-0"
              ></iframe>
              {/* Marker pulse effect */}
              <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 z-10 pointer-events-none">
                <div className="w-8 h-8 bg-accent-500 rounded-full opacity-80 animate-pulse-glow border-4 border-white shadow-lg"></div>
                <MapPin className="w-8 h-8 text-white absolute left-0 top-0" />
              </div>
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-300 z-20 flex items-center justify-center">
                <span className="text-white text-lg font-semibold opacity-0 group-hover:opacity-100 transition-all">View Office Info</span>
              </div>
            </div>
            {/* Office Hours Animated Tabs/Accordion */}
            <div className="flex-1">
              <h2 className="text-xl font-heading font-bold text-primary-900 mb-4">Office Hours</h2>
              <motion.div
                whileHover={{ scale: 1.03, boxShadow: '0 2px 16px 0 #38bdf8aa' }}
                className="rounded-xl bg-white/80 shadow p-4 transition-all border border-neutral-100"
              >
                <div>
                  <div className="font-bold text-primary-900 flex items-center gap-2">
                    General Office Hours
                    <span className="ml-2 w-2 h-2 rounded-full inline-block bg-green-500"></span>
                    <span className="text-xs text-neutral-400 ml-1">Open</span>
                  </div>
                  <div className="text-neutral-600 text-sm">{officeHours.hours}</div>
                  <div className="text-neutral-400 text-xs">{officeHours.weekend}</div>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Persistent Quick-Action Bar */}
      <div className="fixed bottom-0 left-0 right-0 z-50 flex justify-center pointer-events-none">
        <div className="flex gap-6 bg-white/80 backdrop-blur-md rounded-t-2xl shadow-2xl px-8 py-4 mb-2 pointer-events-auto">
          <a
            href="tel:+18764331359"
            className="flex items-center gap-3 px-6 py-3 rounded-xl bg-gradient-to-r from-primary-600 to-primary-500 text-white font-bold text-lg shadow-md hover:shadow-primary-400/30 transition-all duration-300"
            style={{ boxShadow: '0 2px 12px 0 #1e40af66, 0 1px 4px 0 #0001' }}
          >
            <Phone className="w-6 h-6" /> Call Now
          </a>
          <a
            href="https://wa.me/18764331359?text=Hi!%20I%27d%20like%20to%20get%20in%20touch%20with%20EverydayValueJamaica%20regarding%20your%20products%20and%20services."
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center gap-3 px-6 py-3 rounded-xl bg-gradient-to-r from-green-600 to-green-500 text-white font-bold text-lg shadow-md hover:shadow-green-400/30 transition-all duration-300"
            style={{ boxShadow: '0 2px 12px 0 #16a34a66, 0 1px 4px 0 #0001' }}
          >
            <MessageSquare className="w-6 h-6" /> Message on WhatsApp
          </a>
        </div>
      </div>

      {/* Map Modal Popup */}
      {isMapModalOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-[100] bg-black/70 flex items-center justify-center"
          onClick={() => setIsMapModalOpen(false)}
        >
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.8, opacity: 0 }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
            className="bg-white rounded-2xl shadow-2xl max-w-lg w-full p-8 relative"
            onClick={e => e.stopPropagation()}
          >
            <button
              className="absolute top-4 right-4 text-accent-500 hover:text-accent-700 text-2xl font-bold"
              onClick={() => setIsMapModalOpen(false)}
              aria-label="Close"
            >
              ×
            </button>
            <h2 className="text-2xl font-heading font-bold text-primary-900 mb-4">Our Office</h2>
            <img
              src="/images/Everyday_Value_Team_Photo_Final.png"
              alt="Office Photo"
              className="w-full h-48 object-cover rounded-xl mb-4 shadow"
            />
            <div className="mb-2 flex items-center gap-2">
              <MapPin className="w-5 h-5 text-accent-500" />
              <span className="font-semibold">12 Colbeck Avenue, Kingston 10, Jamaica</span>
            </div>
            <div className="mb-2 flex items-center gap-2">
              <Clock className="w-5 h-5 text-accent-500" />
              <span>Mon-Fri: 9am-5pm</span>
            </div>
            <div className="mb-2 flex items-center gap-2">
              <Phone className="w-5 h-5 text-accent-500" />
              <span>(*************</span>
            </div>
            <a
              href="https://maps.google.com/?q=12+Colbeck+Avenue+Kingston+10+Jamaica"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center space-x-2 text-accent-500 hover:text-accent-700 font-semibold mt-4"
            >
              <span>Get Directions</span>
              <MapPin className="w-4 h-4" />
            </a>
          </motion.div>
        </motion.div>
      )}
    </>
  );
};

export default Contact;