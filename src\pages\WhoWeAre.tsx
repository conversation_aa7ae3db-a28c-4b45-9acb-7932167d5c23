import React from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { Award, Users, Heart, Target, Eye, Lightbulb, Package, Truck, Store, Building, Star } from 'lucide-react';
import SEO from '../components/common/SEO';

const WhoWeAre: React.FC = () => {
  const [heroRef, heroInView] = useInView({ triggerOnce: true, threshold: 0.1 });
  const [missionRef, missionInView] = useInView({ triggerOnce: true, threshold: 0.1 });
  const [valuesRef, valuesInView] = useInView({ triggerOnce: true, threshold: 0.1 });
  const [teamRef, teamInView] = useInView({ triggerOnce: true, threshold: 0.1 });
  const [brandsRef, brandsInView] = useInView({ triggerOnce: true, threshold: 0.1 });

  const values = [
    {
      icon: Heart,
      title: 'Integrity',
      description: 'We conduct our business with honesty, transparency, and ethical practices in all our relationships.',
      color: 'text-accent-500'
    },
    {
      icon: Target,
      title: 'Commitment',
      description: 'Our unwavering dedication to serve and deliver quality goods and services to all stakeholders.',
      color: 'text-accent-500'
    },
    {
      icon: Users,
      title: 'Respect',
      description: 'We value and honor all individuals, fostering an environment of mutual respect and understanding.',
      color: 'text-accent-500'
    },
    {
      icon: Award,
      title: 'Accountability',
      description: 'We take responsibility for our actions and decisions, ensuring reliability and trustworthiness.',
      color: 'text-accent-500'
    },
    {
      icon: Lightbulb,
      title: 'Innovation',
      description: 'We embrace new ideas and technologies to continuously improve our services and solutions.',
      color: 'text-accent-500'
    },
    {
      icon: Eye,
      title: 'Quality',
      description: 'We maintain the highest standards in everything we do, from products to customer service.',
      color: 'text-accent-500'
    },
    {
      icon: Package,
      title: 'Development',
      description: 'We invest in continuous improvement and growth for our people, processes, and partnerships.',
      color: 'text-accent-500'
    },
    {
      icon: Star,
      title: 'Excellence',
      description: 'We strive for excellence in all aspects of our business, delivering superior results consistently.',
      color: 'text-accent-500'
    }
  ];

  const stats = [
    { number: '1971', label: 'Established', description: 'Over 50 years of trusted service' },
    { number: '2014', label: 'Company Status', description: 'Obtained company status' },
    { number: '2019', label: 'Rebranded', description: 'Better serve our customers' },
    { number: '✓', label: 'Islandwide', description: 'All major chains and retailers' }
  ];

  const featuredBrands = [
    'Nissin®', 'Brillo®', 'ADA®', 'Chippies®', 'Ti-gaz®',
    'PowerStar®', 'Strawberry Ballers®', 'Cheese Snax®',
    'Total Value®', 'St. Bess Foods®'
  ];

  const customerTypes = [
    { icon: Store, title: 'Chain Stores & Supermarkets', description: 'Major retail chains across Jamaica' },
    { icon: Package, title: 'Wholesales', description: 'Bulk distribution partners' },
    { icon: Building, title: 'Pharmacies', description: 'Healthcare and wellness retailers' },
    { icon: Truck, title: 'Gas Stations', description: 'Convenience and fuel retailers' },
    { icon: Users, title: 'Schools', description: 'Educational institutions' },
    { icon: Building, title: 'Hotels', description: 'Hospitality and tourism sector' }
  ];

  return (
    <>
      <SEO
        title="Who We Are - EverydayValueJamaica"
        description="Learn about EverydayValueJamaica's journey since 1971 as Jamaica's premier supplier of consumer products. Discover our mission, values, and commitment to excellence."
        keywords="about Everyday Value Jamaica, EVJ, Jamaica distribution company, consumer products, mission, values, company history"
      />

      {/* Hero Section */}
      <section className="py-16 sm:py-20 lg:py-24 bg-gradient-to-br from-primary-800 via-primary-700 to-primary-900 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-diagonal-pattern opacity-10"></div>

        <div className="container mx-auto px-6 sm:px-8 relative z-10">
          <motion.div
            ref={heroRef}
            initial={{ opacity: 0, y: 50 }}
            animate={heroInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-heading font-bold mb-6 sm:mb-8">
              About EverydayValueJamaica
            </h1>
            <p className="text-lg sm:text-xl md:text-2xl text-white/90 leading-relaxed">
              Since 1971, EverydayValueJamaica has been a trusted supplier of consumer products,
              serving Jamaica with quality brands in foods, beverages, confectioneries, personal care, household supplies, car accessories, and pet supplies.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Our Story Section */}
      <section className="py-16 sm:py-20 lg:py-24 bg-white">
        <div className="container mx-auto px-6 sm:px-8">
          <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={heroInView ? { opacity: 1, x: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <h2 className="text-2xl sm:text-3xl md:text-4xl font-heading font-bold text-primary-900 mb-6">
                Our Journey
              </h2>
              <div className="space-y-6 text-neutral-600 leading-relaxed">
                <p className="text-lg">
                  <strong>Established in 1971</strong>, EverydayValueJamaica began with a vision to serve
                  Jamaica with quality consumer products. Our business later obtained company status in 2014
                  and underwent a strategic rebranding exercise in 2019 to better serve our customers.
                </p>
                <p>
                  Despite these transitions, EVJ has managed to maintain and build on the relationships forged
                  with our network of retailers and industry leaders who have contributed to our successes over the years.
                </p>
                <p>
                  We are home to many local and international brands, offering comprehensive service support
                  in sales, marketing, inventory control, delivery and merchandising to ensure our partners' success.
                </p>
                <p>
                  <strong>It is our commitment to serve and deliver quality goods and services to all stakeholders, every time.</strong>
                </p>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={heroInView ? { opacity: 1, x: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="relative"
            >
              <img
                src="/images/Everyday_Value_Team_Photo_Final.png"
                alt="Everyday Value Jamaica Limited Team"
                className="rounded-2xl shadow-2xl w-full"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-accent-600/20 to-transparent rounded-2xl"></div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Mission & Vision Section */}
      <section className="py-16 sm:py-20 lg:py-24 bg-neutral-50">
        <div className="container mx-auto px-6 sm:px-8">
          <motion.div
            ref={missionRef}
            initial={{ opacity: 0, y: 50 }}
            animate={missionInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="text-center mb-16 sm:mb-20"
          >
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-heading font-bold text-primary-900 mb-6 sm:mb-8">
              Our Mission & Vision
            </h2>
          </motion.div>

          <div className="grid md:grid-cols-2 gap-8 lg:gap-12 mb-16">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={missionInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow"
            >
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-accent-500 rounded-full flex items-center justify-center mr-4">
                  <Target className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-2xl font-heading font-bold text-primary-900">MISSION</h3>
              </div>
              <p className="text-neutral-600 leading-relaxed text-lg">
                To supply local and international consumer products to all stakeholders across Jamaica through
                competitive prices, advanced technologies and exceptional customer service.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={missionInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow"
            >
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-primary-600 rounded-full flex items-center justify-center mr-4">
                  <Eye className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-2xl font-heading font-bold text-primary-900">VISION</h3>
              </div>
              <p className="text-neutral-600 leading-relaxed text-lg">
                To become the most sought-after distributor within the Caribbean, known for best practices in
                inventory management, delivery solutions, consistent staff training and superior products and services.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* The People We Serve Section */}
      <section className="py-16 sm:py-20 lg:py-24 bg-white">
        <div className="container mx-auto px-6 sm:px-8">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={missionInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="text-center mb-16 sm:mb-20"
          >
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-heading font-bold text-primary-900 mb-6 sm:mb-8">
              The People We Serve
            </h2>
            <p className="text-lg sm:text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed">
              Our strong network of customers includes all major chain stores and supermarkets across the island,
              wholesales, pharmacies, gas stations, schools and hotels.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {customerTypes.map((customer, index) => (
              <motion.div
                key={customer.title}
                initial={{ opacity: 0, y: 30 }}
                animate={missionInView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-neutral-50 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
              >
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-accent-500 rounded-full flex items-center justify-center mr-4">
                    <customer.icon className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-lg font-heading font-bold text-primary-900">{customer.title}</h3>
                </div>
                <p className="text-neutral-600 leading-relaxed text-sm">
                  {customer.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Brands Section */}
      <section className="py-16 sm:py-20 lg:py-24 bg-gradient-to-br from-primary-800 via-primary-700 to-primary-900 text-white">
        <div className="container mx-auto px-6 sm:px-8">
          <motion.div
            ref={brandsRef}
            initial={{ opacity: 0, y: 50 }}
            animate={brandsInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="text-center mb-16 sm:mb-20"
          >
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-heading font-bold mb-6 sm:mb-8">
              Our Brand Portfolio
            </h2>
            <p className="text-lg sm:text-xl text-white/90 max-w-3xl mx-auto leading-relaxed">
              We are home to many local and international brands, including our own Total Value®, St. Bess Foods® and many others.
            </p>
          </motion.div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
            {featuredBrands.map((brand, index) => (
              <motion.div
                key={brand}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={brandsInView ? { opacity: 1, scale: 1 } : {}}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white/10 backdrop-blur-sm rounded-xl p-4 text-center hover:bg-white/20 transition-all duration-300"
              >
                <div className="text-lg font-semibold">
                  {brand}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-16 sm:py-20 lg:py-24 bg-white">
        <div className="container mx-auto px-6 sm:px-8">
          <motion.div
            ref={valuesRef}
            initial={{ opacity: 0, y: 50 }}
            animate={valuesInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="text-center mb-16 sm:mb-20"
          >
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-heading font-bold text-primary-900 mb-6 sm:mb-8">
              Our Core Values
            </h2>
            <p className="text-lg sm:text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed">
              These principles guide every decision we make and every relationship we build.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <motion.div
                key={value.title}
                initial={{ opacity: 0, y: 30 }}
                animate={valuesInView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="text-center group"
              >
                <div className="bg-neutral-50 rounded-2xl p-8 hover:shadow-lg transition-all duration-300 group-hover:-translate-y-2">
                  <div className={`w-16 h-16 bg-accent-100 rounded-full flex items-center justify-center mx-auto mb-6`}>
                    <value.icon className={`w-8 h-8 ${value.color}`} />
                  </div>
                  <h3 className="text-xl font-heading font-bold text-primary-900 mb-4">
                    {value.title}
                  </h3>
                  <p className="text-neutral-600 leading-relaxed">
                    {value.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Company Timeline Section */}
      <section className="py-16 sm:py-20 lg:py-24 bg-neutral-50">
        <div className="container mx-auto px-6 sm:px-8">
          <motion.div
            ref={teamRef}
            initial={{ opacity: 0, y: 50 }}
            animate={teamInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="text-center mb-16 sm:mb-20"
          >
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-heading font-bold text-primary-900 mb-6 sm:mb-8">
              Our Journey Through Time
            </h2>
            <p className="text-lg sm:text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed">
              Key milestones that reflect our growth and commitment to serving Jamaica.
            </p>
          </motion.div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={teamInView ? { opacity: 1, scale: 1 } : {}}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-3 text-accent-500">
                  {stat.number}
                </div>
                <div className="text-lg sm:text-xl font-semibold mb-2 text-primary-900">
                  {stat.label}
                </div>
                <div className="text-sm sm:text-base text-neutral-600">
                  {stat.description}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    </>
  );
};

export default WhoWeAre;
