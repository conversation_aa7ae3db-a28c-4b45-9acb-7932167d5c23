import React from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { Factory, Settings, Award, CheckCircle, Users, Clock, Shield, Package, Cog, Beaker, Truck, Star, ShoppingCart, Heart } from 'lucide-react';
import { Link } from 'react-router-dom';
import SEO from '../components/common/SEO';

const Manufacturing: React.FC = () => {
  const [heroRef, heroInView] = useInView({ triggerOnce: true, threshold: 0.1 });
  const [servicesRef, servicesInView] = useInView({ triggerOnce: true, threshold: 0.1 });
  const [processRef, processInView] = useInView({ triggerOnce: true, threshold: 0.1 });

  const manufacturingServices = [
    {
      icon: Factory,
      title: 'Contract Manufacturing',
      description: 'Full-scale manufacturing services with state-of-the-art facilities and quality control systems.',
      features: ['Custom Product Development', 'Scalable Production Lines', 'Quality Assurance Testing', 'Regulatory Compliance']
    },
    {
      icon: Package,
      title: 'Private Label Development',
      description: 'Complete private label solutions from concept to finished product with your brand identity.',
      features: ['Brand Development', 'Product Formulation', 'Packaging Design', 'Market Research']
    },
    {
      icon: Cog,
      title: 'Co-Packing Services',
      description: 'Efficient co-packing solutions with flexible capacity and specialized equipment.',
      features: ['Flexible Packaging Options', 'Specialized Equipment', 'Inventory Management', 'Just-in-Time Production']
    }
  ];

  const manufacturingProcess = [
    {
      step: '01',
      title: 'Consultation & Planning',
      description: 'Understanding your requirements, market goals, and production specifications.',
      icon: Users
    },
    {
      step: '02',
      title: 'Product Development',
      description: 'Formulation, testing, and refinement to create the perfect product.',
      icon: Beaker
    },
    {
      step: '03',
      title: 'Production Setup',
      description: 'Setting up production lines with quality control and efficiency optimization.',
      icon: Settings
    },
    {
      step: '04',
      title: 'Manufacturing & Quality',
      description: 'Full-scale production with rigorous quality control and compliance standards.',
      icon: Factory
    }
  ];

  const capabilities = [
    'State-of-the-art manufacturing facilities',
    'ISO certified quality management systems',
    'Flexible production capacity scaling',
    'Advanced packaging technologies',
    'Regulatory compliance expertise',
    'Custom formulation capabilities',
    'Rapid prototyping and testing',
    'Supply chain optimization'
  ];

  return (
    <>
      <SEO
        title="Manufacturing Services - Everyday Value Jamaica"
        description="Professional manufacturing services including contract manufacturing, private label development, co-packing, and product formulation across Jamaica."
        keywords="manufacturing Jamaica, contract manufacturing, private label, co-packing, product formulation, manufacturing services"
      />

      {/* Hero Section */}
      <section className="py-16 sm:py-20 lg:py-24 bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700 text-white">
        <div className="container mx-auto px-6 sm:px-8">
          <motion.div
            ref={heroRef}
            initial={{ opacity: 0, y: 50 }}
            animate={heroInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2, duration: 0.6 }}
              className="inline-flex items-center space-x-2 bg-accent-500/20 backdrop-blur-sm border border-accent-400/30 rounded-full px-6 py-3 mb-8"
            >
              <Factory className="w-5 h-5 text-accent-400" />
              <span className="text-accent-400 font-medium">Manufacturing Excellence</span>
            </motion.div>

            <motion.h1
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.8 }}
              className="text-4xl sm:text-5xl md:text-6xl font-heading font-bold mb-8 leading-tight"
            >
              Manufacturing Services
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.8 }}
              className="text-xl sm:text-2xl text-neutral-200 max-w-4xl mx-auto leading-relaxed mb-12"
            >
              State-of-the-art manufacturing facilities with cutting-edge technology for contract manufacturing, 
              private labeling, and co-packing services across Jamaica.
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8, duration: 0.8 }}
              className="flex flex-col sm:flex-row gap-4 justify-center"
            >
              <Link
                to="/contact"
                className="inline-flex items-center space-x-3 bg-accent-500 hover:bg-accent-600 text-white px-8 py-4 rounded-full font-semibold text-lg transition-all duration-300"
              >
                <Factory className="w-6 h-6" />
                <span>Start Manufacturing</span>
              </Link>
              <Link
                to="/distribution"
                className="inline-flex items-center space-x-3 border-2 border-white text-white hover:bg-white hover:text-primary-900 px-8 py-4 rounded-full font-semibold text-lg transition-all duration-300"
              >
                <Truck className="w-6 h-6" />
                <span>View Distribution</span>
              </Link>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Manufacturing Services */}
      <section className="py-16 sm:py-20 lg:py-24 bg-white">
        <div className="container mx-auto px-6 sm:px-8">
          <motion.div
            ref={servicesRef}
            initial={{ opacity: 0, y: 50 }}
            animate={servicesInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl sm:text-4xl md:text-5xl font-heading font-bold text-primary-900 mb-6">
              Our Manufacturing Services
            </h2>
            <p className="text-lg sm:text-xl text-neutral-600 max-w-4xl mx-auto leading-relaxed">
              Comprehensive manufacturing solutions designed to bring your products to market with excellence and efficiency.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8">
            {manufacturingServices.map((service, index) => (
              <motion.div
                key={service.title}
                initial={{ opacity: 0, y: 30 }}
                animate={servicesInView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-neutral-50 rounded-3xl p-8 hover:shadow-lg transition-shadow h-full flex flex-col"
              >
                <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-accent-500 to-accent-600 flex items-center justify-center mb-6">
                  <service.icon className="w-8 h-8 text-white" />
                </div>

                <h3 className="text-lg sm:text-xl lg:text-2xl font-heading font-bold text-primary-900 mb-4 leading-tight min-h-[3.5rem] flex items-center">
                  {service.title}
                </h3>

                <p className="text-neutral-600 leading-relaxed mb-6 flex-grow">
                  {service.description}
                </p>

                <ul className="space-y-3">
                  {service.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start space-x-3">
                      <CheckCircle className="w-5 h-5 text-accent-500 flex-shrink-0 mt-0.5" />
                      <span className="text-neutral-700 font-medium text-sm leading-relaxed">{feature}</span>
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Products */}
      <section className="py-16 sm:py-20 lg:py-24 bg-gradient-to-br from-neutral-50 to-white">
        <div className="container mx-auto px-6 sm:px-8">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <div className="inline-flex items-center space-x-2 bg-accent-500/10 backdrop-blur-sm border border-accent-400/20 rounded-full px-6 py-3 mb-6">
              <Star className="w-5 h-5 text-accent-600" />
              <span className="text-accent-600 font-medium">Featured Products</span>
            </div>
            <h2 className="text-3xl sm:text-4xl md:text-5xl font-heading font-bold text-primary-900 mb-6">
              We Manufacture
            </h2>
            <p className="text-lg sm:text-xl text-neutral-600 max-w-4xl mx-auto leading-relaxed">
              Quality products crafted with precision and care, bringing authentic Jamaican flavors to your table.
            </p>
          </motion.div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 lg:gap-8 max-w-6xl mx-auto">
            {[
              {
                name: 'St. Bess Peanut Brittle',
                image: '/images/St. Bess Peanut Brittle.png',
                category: 'Confectionery'
              },
              {
                name: "Lowes' Peanut Brittle",
                image: '/images/St. Bess Peanut Brittle.png',
                category: 'Confectionery'
              },
              {
                name: 'Ground Pimento',
                image: '/images/St. Bess Pimento.png',
                category: 'Spices'
              },
              {
                name: 'Ground Nutmeg',
                image: '/images/St. Bess Nutmeg.png',
                category: 'Spices'
              }
            ].map((product, index) => (
              <motion.div
                key={product.name}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5, scale: 1.02 }}
                className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-neutral-100 group"
              >
                <div className="aspect-square flex items-center justify-center mb-4 bg-gradient-to-br from-neutral-50 to-neutral-100 rounded-xl p-4 group-hover:from-accent-50 group-hover:to-accent-100 transition-all duration-300">
                  <img
                    src={product.image}
                    alt={product.name}
                    className="max-w-full max-h-full object-contain filter group-hover:brightness-110 transition-all duration-300"
                  />
                </div>
                <div className="text-center">
                  <span className="inline-block text-xs font-medium text-accent-600 bg-accent-100 px-3 py-1 rounded-full mb-2">
                    {product.category}
                  </span>
                  <h4 className="font-semibold text-primary-900 leading-tight text-sm">
                    {product.name}
                  </h4>
                </div>
              </motion.div>
            ))}
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            viewport={{ once: true }}
            className="text-center mt-12"
          >
            <div className="inline-flex items-center space-x-2 bg-neutral-100 rounded-full px-4 py-2">
              <Clock className="w-4 h-4 text-neutral-600" />
              <span className="text-sm text-neutral-600 font-medium">
                Canned Ackee & Canned Callaloo - Coming Soon
              </span>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Our Happy Customers */}
      <section className="py-16 sm:py-20 lg:py-24 bg-white">
        <div className="container mx-auto px-6 sm:px-8">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <div className="inline-flex items-center space-x-2 bg-accent-500/10 backdrop-blur-sm border border-accent-400/20 rounded-full px-6 py-3 mb-6">
              <Star className="w-5 h-5 text-accent-600" />
              <span className="text-accent-600 font-medium">Trusted Partners</span>
            </div>
            <h2 className="text-3xl sm:text-4xl md:text-5xl font-heading font-bold text-primary-900 mb-6">
              Our Happy Customers
            </h2>
            <p className="text-lg sm:text-xl text-neutral-600 max-w-4xl mx-auto leading-relaxed">
              Proud to serve leading supermarkets and pharmacies across Jamaica with reliable manufacturing
              services.
            </p>
          </motion.div>

          {/* Supermarkets Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="mb-20"
          >
            <div className="flex items-center justify-center mb-12">
              <div className="flex items-center space-x-3 bg-gradient-to-r from-primary-600 to-primary-700 text-white px-8 py-4 rounded-full shadow-lg">
                <ShoppingCart className="w-6 h-6" />
                <h3 className="text-2xl font-heading font-bold">Supermarkets</h3>
              </div>
            </div>

            <div className="flex flex-wrap items-center justify-center gap-8 lg:gap-12">
              {[
                { name: 'Progressive Grocers', image: '/images/Happy customers Supermarket/Progressive grocers.png' },
                { name: 'Shoppers Fair', image: '/images/Happy customers Supermarket/Shoppers fair logo.png' },
                { name: 'General Foods', image: '/images/Happy customers Supermarket/General foods logo.png' },
                { name: 'Boot Stores', image: '/images/Happy customers Supermarket/boot logo.jpg' },
                { name: 'Fresh Foods', image: '/images/Happy customers Supermarket/fresh foods logo.jpg' },
                { name: 'MegaMart', image: '/images/Happy customers Supermarket/megamart.jpg' },
                { name: 'JRW', image: '/images/Happy customers Supermarket/JRW logo.jpg' }
              ].map((customer, index) => (
                <motion.div
                  key={customer.name}
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ scale: 1.1 }}
                  className="group cursor-pointer"
                >
                  <div className="w-40 h-32 lg:w-48 lg:h-36 flex items-center justify-center p-4 transition-all duration-300 group-hover:drop-shadow-lg">
                    <img
                      src={customer.image}
                      alt={customer.name}
                      className="max-w-full max-h-full object-contain transition-all duration-300"
                    />
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Pharmacies Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
          >
            <div className="flex items-center justify-center mb-12">
              <div className="flex items-center space-x-3 bg-gradient-to-r from-accent-500 to-accent-600 text-white px-8 py-4 rounded-full shadow-lg">
                <Heart className="w-6 h-6" />
                <h3 className="text-2xl font-heading font-bold">Pharmacies</h3>
              </div>
            </div>

            <div className="flex flex-wrap items-center justify-center gap-12 lg:gap-16">
              {[
                { name: 'Fontana Pharmacy', image: '/images/Happy customers pharmacy/Fontana Jamaica Logo 160 x 65.png' },
                { name: 'Lees Family Pharmacy', image: '/images/Happy customers pharmacy/lees pharmacy logo.png' },
                { name: 'Supermad Pharmacy', image: '/images/Happy customers pharmacy/supermad logo.jpg' }
              ].map((customer, index) => (
                <motion.div
                  key={customer.name}
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.6, delay: index * 0.15 }}
                  viewport={{ once: true }}
                  whileHover={{ scale: 1.1 }}
                  className="group cursor-pointer"
                >
                  <div className="w-48 h-36 lg:w-56 lg:h-40 flex items-center justify-center p-6 transition-all duration-300 group-hover:drop-shadow-lg">
                    <img
                      src={customer.image}
                      alt={customer.name}
                      className="max-w-full max-h-full object-contain transition-all duration-300"
                    />
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Manufacturing Process */}
      <section className="py-16 sm:py-20 lg:py-24 bg-neutral-50">
        <div className="container mx-auto px-6 sm:px-8">
          <motion.div
            ref={processRef}
            initial={{ opacity: 0, y: 50 }}
            animate={processInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl sm:text-4xl md:text-5xl font-heading font-bold text-primary-900 mb-6">
              Our Manufacturing Process
            </h2>
            <p className="text-lg sm:text-xl text-neutral-600 max-w-4xl mx-auto leading-relaxed">
              From concept to production, our streamlined process ensures quality and efficiency at every step.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {manufacturingProcess.map((step, index) => (
              <motion.div
                key={step.step}
                initial={{ opacity: 0, y: 30 }}
                animate={processInView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="bg-white rounded-3xl p-8 shadow-lg hover:shadow-xl transition-shadow"
              >
                <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-accent-500 to-accent-600 flex items-center justify-center mx-auto mb-6">
                  <step.icon className="w-8 h-8 text-white" />
                </div>

                <div className="text-3xl font-bold text-accent-500 mb-4 text-center">
                  {step.step}
                </div>

                <h3 className="text-xl font-heading font-bold text-primary-900 mb-4 text-center">
                  {step.title}
                </h3>

                <p className="text-neutral-600 leading-relaxed text-center">
                  {step.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Capabilities & CTA */}
      <section className="py-16 sm:py-20 lg:py-24 bg-gradient-to-br from-primary-900 to-primary-800 text-white">
        <div className="container mx-auto px-6 sm:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={processInView ? { opacity: 1, x: 0 } : {}}
              transition={{ duration: 0.8 }}
            >
              <h2 className="text-3xl sm:text-4xl font-heading font-bold mb-6">
                Manufacturing Capabilities
              </h2>
              <p className="text-xl text-neutral-200 leading-relaxed mb-8">
                Our advanced manufacturing capabilities ensure your products meet the highest standards of quality and efficiency.
              </p>

              <div className="grid sm:grid-cols-2 gap-4">
                {capabilities.map((capability, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={processInView ? { opacity: 1, x: 0 } : {}}
                    transition={{ duration: 0.8, delay: index * 0.1 }}
                    className="flex items-center space-x-3"
                  >
                    <CheckCircle className="w-5 h-5 text-accent-400 flex-shrink-0" />
                    <span className="text-neutral-200">{capability}</span>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={processInView ? { opacity: 1, x: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="bg-white/10 backdrop-blur-sm rounded-3xl p-8"
            >
              <div className="text-center">
                <Factory className="w-16 h-16 text-accent-400 mx-auto mb-6" />
                <h3 className="text-2xl font-heading font-bold mb-4">
                  Ready to Start Manufacturing?
                </h3>
                <p className="text-neutral-200 leading-relaxed mb-8">
                  Let's discuss your manufacturing needs and create a custom solution that brings your vision to life.
                </p>
                <Link
                  to="/contact"
                  className="inline-flex items-center space-x-3 bg-accent-500 hover:bg-accent-600 text-white px-8 py-4 rounded-full font-semibold text-lg transition-all duration-300"
                >
                  <Users className="w-6 h-6" />
                  <span>Get Started</span>
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </>
  );
};

export default Manufacturing;
