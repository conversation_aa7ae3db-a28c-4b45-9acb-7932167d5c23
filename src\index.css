@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Safari and Chrome */
  }
}

/* Ensure images load properly and handle errors gracefully */
img {
  max-width: 100%;
  height: auto;
}

/* Fix for hero background images */
.hero-image {
  object-fit: cover;
  object-position: center;
  width: 100%;
  height: 100%;
  min-height: 100vh;
  min-width: 100vw;
}

/* Prevent layout shift during image loading */
.aspect-ratio-container {
  position: relative;
  overflow: hidden;
}

.aspect-ratio-container::before {
  content: '';
  display: block;
  padding-top: 100%; /* 1:1 aspect ratio */
}

.aspect-ratio-container > * {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}