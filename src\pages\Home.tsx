import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';
import { useInView } from 'react-intersection-observer';
import { ArrowRight, Star, ChevronDown, Play, Award, Users, Truck, Heart } from 'lucide-react';
import SEO from '../components/common/SEO';
import FeaturedBrandsCarousel from '../components/FeaturedBrandsCarousel';
import { brands } from '../data/brands';

const Home: React.FC = () => {
  const [heroRef, heroInView] = useInView({ triggerOnce: true, threshold: 0.1 });
  const [aboutRef, aboutInView] = useInView({ triggerOnce: true, threshold: 0.1 });


  // Hero slider state
  const [currentSlide, setCurrentSlide] = useState(0);

  const heroImages = [
    '/images/ChatGPT Image Jun 24, 2025, 07_50_13 PM.png',
    '/images/ChatGPT Image Jun 24, 2025, 07_47_34 PM.png',
    '/images/ChatGPT Image Jun 24, 2025, 10_57_25 PM.png'
  ];

  // Auto-advance slides every 6 seconds
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % heroImages.length);
    }, 6000);

    return () => clearInterval(timer);
  }, [heroImages.length]);



  const stats = [
    { icon: Award, number: '50+', label: 'Years in Business', color: 'text-accent-500' },
    { icon: Users, number: '100+', label: 'Brands Supported', color: 'text-blue-500' },
    { icon: Truck, number: '✓', label: 'Islandwide Delivery', color: 'text-green-500' },
    { icon: Heart, number: '300+', label: 'Happy Customers', color: 'text-red-500' }
  ];

  return (
    <>
      <SEO
        title="Everyday Value Jamaica - Jamaica's Premier Distribution Partner"
        description="Best Brands. Best Price. Best Choice. Over 20 years of distribution excellence across Jamaica. Your trusted partner for quality products and reliable service."
        keywords="Jamaica distribution, wholesale, retail, brands, products, Everyday Value Jamaica, Kingston, islandwide delivery"
      />

      {/* Hero Section with Silent Background Carousel */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Background Image Carousel */}
        <div className="absolute inset-0 z-0">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentSlide}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 2, ease: "easeInOut" }}
              className="absolute inset-0 w-full h-full"
            >
              <img
                src={heroImages[currentSlide]}
                alt={`Everyday Value Jamaica Background ${currentSlide + 1}`}
                className="w-full h-full object-cover object-center"
                style={{
                  minHeight: '100vh',
                  minWidth: '100vw'
                }}
                onError={(e) => {
                  console.error('Image failed to load:', heroImages[currentSlide]);
                  // Fallback to a solid color background
                  (e.target as HTMLImageElement).style.display = 'none';
                }}
              />
              {/* Fallback background in case image fails */}
              <div 
                className="absolute inset-0 bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700"
                style={{ 
                  display: 'none' // Will be shown if image fails to load
                }}
              />
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Optimized Gradient Overlays for Better Text Legibility */}
        <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-black/40 to-black/60 z-10"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-black/30 z-10"></div>

        {/* Floating Elements */}
        <div className="absolute inset-0 overflow-hidden z-15">
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-accent-400 rounded-full opacity-60"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [-20, 20, -20],
                opacity: [0.3, 0.8, 0.3],
              }}
              transition={{
                duration: 4 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>

        {/* Content */}
        <div className="relative z-20 container mx-auto px-6 sm:px-8 text-center">
          <motion.div
            ref={heroRef}
            initial={{ opacity: 0, y: 50 }}
            animate={heroInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="max-w-5xl mx-auto"
          >
            {/* Badge */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2, duration: 0.6 }}
              className="inline-flex items-center space-x-2 bg-accent-500/20 backdrop-blur-sm border border-accent-400/30 rounded-full px-4 sm:px-6 py-2 sm:py-3 mb-8 sm:mb-12"
            >
              <Star className="w-4 h-4 text-accent-400" />
              <span className="text-accent-400 text-sm sm:text-base font-medium">Jamaica's Premier Distribution Partner</span>
            </motion.div>

            {/* Main Headline */}
            <motion.h1
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.8 }}
              className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-heading font-bold text-white mb-8 sm:mb-12 leading-tight drop-shadow-lg"
            >
              <span className="inline">Best Brands. </span>
              <span className="inline text-accent-400">Best Price. </span>
              <span className="inline">Best Choice.</span>
            </motion.h1>

            {/* Subtitle */}
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.8 }}
              className="text-lg sm:text-xl md:text-2xl text-neutral-200 mb-12 sm:mb-16 max-w-4xl mx-auto leading-relaxed drop-shadow-md"
            >
              Delivering Jamaica's favorite products nationwide with{' '}
              <span className="text-accent-400 font-semibold">50+ years of excellence</span>{' '}
              and unmatched service quality.
            </motion.p>

            {/* CTA Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8, duration: 0.8 }}
              className="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center items-center mb-16 sm:mb-20"
            >
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Link
                  to="/brands"
                  className="group bg-accent-500 hover:bg-accent-600 text-white px-6 sm:px-8 py-3 sm:py-4 rounded-full font-semibold text-base sm:text-lg transition-all duration-300 flex items-center justify-center shadow-lg hover:shadow-xl w-full sm:w-auto"
                >
                  <span>Explore Brands</span>
                </Link>
              </motion.div>

              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Link
                  to="/contact"
                  className="group border-2 border-white text-white hover:bg-white hover:text-primary-900 px-6 sm:px-8 py-3 sm:py-4 rounded-full font-semibold text-base sm:text-lg transition-all duration-300 w-full sm:w-auto"
                >
                  Contact Us
                </Link>
              </motion.div>
            </motion.div>

            {/* Stats */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1, duration: 0.8 }}
              className="grid grid-cols-1 sm:grid-cols-3 gap-8 sm:gap-12 max-w-3xl mx-auto"
            >
              {[
                { number: '50+', label: 'Years in Business' },
                { number: '100+', label: 'Brands Supported' },
                { number: '✓', label: 'Islandwide Coverage' }
              ].map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-3xl sm:text-4xl md:text-5xl font-bold text-accent-400 mb-3 drop-shadow-lg">
                    {stat.number}
                  </div>
                  <div className="text-neutral-300 text-sm sm:text-base drop-shadow-md">
                    {stat.label}
                  </div>
                </div>
              ))}
            </motion.div>
          </motion.div>
        </div>

        {/* Scroll Indicator */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.5, duration: 0.8 }}
          className="absolute bottom-8 sm:bottom-12 left-1/2 transform -translate-x-1/2 z-20"
        >
          <motion.div
            className="flex flex-col items-center text-white hover:text-accent-400 transition-colors cursor-pointer"
            animate={{ y: [0, 10, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
            onClick={() => document.getElementById('about')?.scrollIntoView({ behavior: 'smooth' })}
          >
            <span className="text-sm mb-2 hidden sm:block drop-shadow-md">Scroll to explore</span>
            <ChevronDown className="w-6 h-6 drop-shadow-md" />
          </motion.div>
        </motion.div>
      </section>

      {/* About Preview Section */}
      <section id="about" className="py-16 sm:py-20 lg:py-24 bg-neutral-50 relative overflow-hidden">
        <div className="absolute inset-0 bg-diagonal-pattern opacity-5"></div>
        
        <div className="container mx-auto px-6 sm:px-8 relative z-10">
          <motion.div
            ref={aboutRef}
            initial={{ opacity: 0, y: 50 }}
            animate={aboutInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="text-center mb-16 sm:mb-20"
          >
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-heading font-bold text-primary-900 mb-6 sm:mb-8">
              About Everyday Value Jamaica
            </h2>
            <p className="text-lg sm:text-xl text-neutral-600 max-w-4xl mx-auto leading-relaxed px-4">
              For over two decades, we've been Jamaica's trusted partner in product distribution, 
              connecting communities with the brands they love.
            </p>
          </motion.div>

          {/* Image Placeholder Section - Space for Future Images */}
          <div className="flex flex-col lg:flex-row gap-10 py-16 items-center max-w-7xl mx-auto px-6">
            {/* Left: Image Placeholder */}
            <div className="w-full lg:w-1/2 flex justify-center">
              <div className="w-full max-w-md mx-auto h-64 bg-neutral-100 rounded-xl shadow-lg flex items-center justify-center border-2 border-dashed border-neutral-300">
                <div className="text-center text-neutral-500">
                  <div className="text-4xl mb-2">📷</div>
                  <p className="text-sm">Image placeholder</p>
                </div>
              </div>
            </div>
            {/* Right: Image Placeholder */}
            <div className="w-full lg:w-1/2 flex justify-center">
              <div className="w-full max-w-md mx-auto h-64 bg-neutral-100 rounded-xl shadow-lg flex items-center justify-center border-2 border-dashed border-neutral-300">
                <div className="text-center text-neutral-500">
                  <div className="text-4xl mb-2">📷</div>
                  <p className="text-sm">Image placeholder</p>
                </div>
              </div>
            </div>
          </div>

          {/* Stats */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={aboutInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="grid grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8"
          >
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={aboutInView ? { opacity: 1, scale: 1 } : {}}
                transition={{ duration: 0.6, delay: 1 + index * 0.1 }}
                className="text-center group"
              >
                <div className="bg-white rounded-xl p-6 sm:p-8 shadow-lg hover:shadow-xl transition-all duration-300 group-hover:-translate-y-2">
                  <stat.icon className={`w-8 h-8 sm:w-10 sm:h-10 ${stat.color} mx-auto mb-4`} />
                  <div className="text-2xl sm:text-3xl font-bold text-primary-900 mb-2">
                    {stat.number}
                  </div>
                  <div className="text-neutral-600 text-sm sm:text-base">
                    {stat.label}
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Enhanced Featured Brands Carousel */}
      <FeaturedBrandsCarousel />


    </>
  );
};

export default Home;