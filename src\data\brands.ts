export interface Brand {
  id: string;
  name: string;
  logo: string;
  description: string;
  category: string[];
  featured: boolean;
}

export interface Product {
  id: string;
  name: string;
  brand: string;
  category: string;
  image: string;
  description: string;
  tags: string[];
  ingredients?: string;
  weight?: string;
  origin?: string;
}

export const brands: Brand[] = [
  {
    id: 'ada',
    name: 'ADA',
    logo: '/images/Ada logo.png',
    description: 'Premium household supplies, beverages, and everyday essentials',
    category: ['household', 'beverages', 'personal-care', 'food'],
    featured: true
  },
  {
    id: 'chippies',
    name: 'Chippies',
    logo: '/images/CHIPPIES_LOGO.png',
    description: 'Delicious crispy snacks and chips for every occasion',
    category: ['snacks', 'confectionery'],
    featured: true
  },
  {
    id: 'happy-snacks',
    name: 'Happy Snacks',
    logo: '/images/Happy-Snacks.png',
    description: 'Bringing joy with every bite - premium snacks and treats',
    category: ['confectionery', 'snacks'],
    featured: true
  },
  {
    id: 'nicies',
    name: 'Nicies',
    logo: '/images/Nicies-header-logo.png',
    description: 'Quality confectionery and sweet treats',
    category: ['confectionery', 'snacks'],
    featured: true
  },
  {
    id: 'st-bess',
    name: 'St Bess',
    logo: '/images/st-bess.png',
    description: 'Premium food products and pantry essentials made in Jamaica',
    category: ['food', 'pantry'],
    featured: true
  }
];

export const products: Product[] = [
  // ADA Products - One representative product
  {
    id: 'ada-liquid-laundry-detergent-gold-2l',
    name: 'ADA Gold 2L',
    brand: 'ADA',
    category: 'household',
    image: '/images/ADA-Gold-2L-EVJ.png',
    description: 'Premium liquid laundry detergent that delivers exceptional cleaning power while being gentle on fabrics.',
    tags: ['detergent', 'liquid', 'gold', '2L']
  },

  // Chippies Products - One representative product
  {
    id: 'chippies-banana-chips',
    name: 'Chippies Banana Chips',
    brand: 'Chippies',
    category: 'snacks',
    image: '/images/CHIPPIES-BANANA-CHIPS-EVJ.png',
    description: 'Famous banana chips from Chippies - crispy and delicious.',
    tags: ['banana', 'chips', 'snack']
  },

  // Happy Snacks Products - One representative product
  {
    id: 'happy-snacks-sour-gummy-bears',
    name: 'Happy Snacks Sour Gummy Bears',
    brand: 'Happy Snacks',
    category: 'confectionery',
    image: '/images/SOUR-GUMMY-BEARS.png',
    description: 'Sour gummy bears bursting with fruity flavor.',
    tags: ['sour', 'gummy', 'bears', 'candy']
  },

  // Nicies Products - One representative product
  {
    id: 'nicies-lime-juice-750ml',
    name: 'Nicies Lime Juice 750ml',
    brand: 'Nicies',
    category: 'beverages',
    image: '/images/NICIES-Products-Limejuice-750ml-EVJ.png',
    description: 'Fresh lime juice in larger 750ml bottle - ideal for families and restaurants',
    tags: ['juice', 'lime', 'cooking', 'fresh', 'family-size'],
    weight: '750ml',
    origin: 'Product of Jamaica'
  },

  // St Bess Products - One representative product
  {
    id: 'st-bess-peanut-brittle',
    name: 'St Bess Peanut Brittle',
    brand: 'St Bess',
    category: 'confectionery',
    image: '/images/ST.-BESS-PEANUT-BRITTLE-png.png',
    description: 'Authentic Jamaican peanut brittle - crispy, sweet treat made with local peanuts',
    tags: ['confectionery', 'local', 'jamaican', 'sweet'],
    weight: '50g (2 Bars)',
    origin: 'Product of Jamaica'
  }
];

export const categories = [
  { id: 'all', name: 'All Categories', icon: 'Grid' },
  { id: 'beverages', name: 'Beverages', icon: 'Coffee' },
  { id: 'confectionery', name: 'Confectionery', icon: 'Candy' },
  { id: 'snacks', name: 'Snacks', icon: 'Cookie' },
  { id: 'household', name: 'Household Supplies', icon: 'Home' },
  { id: 'food', name: 'Foods', icon: 'UtensilsCrossed' },
  { id: 'personal-care', name: 'Personal Care', icon: 'Heart' },
  { id: 'car-accessories', name: 'Car Accessories', icon: 'Car' },
  { id: 'pet-supplies', name: 'Pet Supplies', icon: 'Heart' },
  { id: 'pantry', name: 'Pantry Essentials', icon: 'Package' }
];