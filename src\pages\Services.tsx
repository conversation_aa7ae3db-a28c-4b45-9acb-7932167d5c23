import React from 'react';
import { motion } from 'framer-motion';
import { BarChart3, Truck, Warehouse, Mail, Phone, ArrowRight, TrendingUp, Lightbulb, Globe, Clock, ShieldCheck, Handshake } from 'lucide-react';
import SEO from '../components/common/SEO';
const servicesBg = '/images/services.png';

const brandColors = {
  blue: '#244296',
  green: '#1d813f',
  purple: '#8133b5',
  yellow: '#fcb900'
};

const services = [
  {
    icon: BarChart3,
    color: brandColors.blue,
    title: 'Marketing and Sales',
    summary: 'Strategic marketing and sales solutions to grow your brand.',
    details: `We help our suppliers set and achieve sales targets through customized advertising and marketing. Everyday Value Jamaica integrates marketing for both suppliers and retailers, ensuring a strong presence in Jamaica's competitive retail market.`,
    contact: '<EMAIL>',
    phone: '(*************',
  },
  {
    icon: Truck,
    color: brandColors.green,
    title: 'Delivery and Merchandising',
    summary: 'Islandwide delivery and expert merchandising for all categories.',
    details:
      'We deliver high-quality consumer brands across Jamaica. Our trained merchandisers maintain the highest standards in product placement and stock replenishment, ensuring your products stand out in every store.',
    contact: '<EMAIL>',
    phone: '(*************',
  },
  {
    icon: Warehouse,
    color: brandColors.purple,
    title: 'Warehousing and Customer Care',
    summary: 'Reliable warehousing and dedicated customer support.',
    details:
      'We use best practices in warehouse management and inventory control. Our in-house customer support team helps stores with orders and queries, ensuring smooth operations through our UOrder platform and direct communication.',
    contact: '<EMAIL>',
    phone: '(*************',
  },
];

const processSteps = [
  {
    number: '01',
    color: brandColors.blue,
    title: 'Consultation',
    desc: 'We assess your distribution needs and develop a tailored strategy.'
  },
  {
    number: '02',
    color: brandColors.green,
    title: 'Planning',
    desc: 'Our team creates a comprehensive plan to maximize your brand\'s reach.'
  },
  {
    number: '03',
    color: brandColors.purple,
    title: 'Implementation',
    desc: 'We execute the plan with precision, ensuring quality at every step.'
  },
  {
    number: '04',
    color: brandColors.yellow,
    title: 'Monitoring',
    desc: 'Continuous monitoring and optimization to ensure your success.'
  },
];

const serviceFeatures = [
  [
    { icon: TrendingUp, label: 'Growth Focused' },
    { icon: Lightbulb, label: 'Custom Strategies' }
  ],
  [
    { icon: Globe, label: 'Islandwide' },
    { icon: Clock, label: 'On-Time Delivery' }
  ],
  [
    { icon: ShieldCheck, label: 'Secure Storage' },
    { icon: Handshake, label: 'Personal Support' }
  ]
];

const Services: React.FC = () => {
  return (
    <>
      <SEO
        title="Our Services - EverydayValueJamaica"
        description="Comprehensive distribution solutions including marketing & sales, delivery & merchandising, and warehousing & customer care. Maximize your brand's reach across Jamaica."
        keywords="distribution services, marketing, sales, delivery, merchandising, warehousing, customer care, Jamaica, EverydayValueJamaica"
      />
      <div className="pt-20 font-sans bg-neutral-50 relative overflow-hidden">
        {/* Header with background image and gradient overlay */}
        <section
          className="relative w-full min-h-[320px] sm:min-h-[400px] flex items-center justify-center mb-12"
          style={{
            backgroundImage: `linear-gradient(to bottom, rgba(20,30,50,0.7) 60%, rgba(20,30,50,0.1) 100%), url(${servicesBg})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
          }}
        >
          <div className="w-full max-w-3xl mx-auto px-6 sm:px-8 py-16 sm:py-24 flex flex-col items-center justify-center text-center">
            {/* Optional logo with colored 'U' in Value */}
            <div className="flex items-center justify-center mb-6 select-none">
              <span className="font-heading font-bold text-2xl sm:text-3xl md:text-4xl text-white tracking-tight">EVERYDAY VAL</span>
              <span className="font-heading font-bold text-2xl sm:text-3xl md:text-4xl text-yellow-400 tracking-tight">U</span>
              <span className="font-heading font-bold text-2xl sm:text-3xl md:text-4xl text-white tracking-tight">E</span>
            </div>
            <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-white mb-6 sm:mb-8 drop-shadow-lg">Our Services</h1>
            <p className="hidden md:block text-base md:text-lg text-white/90 max-w-2xl mx-auto leading-relaxed px-4 drop-shadow">
              Comprehensive distribution solutions designed to maximize your brand's reach and impact across Jamaica. From marketing strategy to final delivery, we handle every aspect of your distribution needs.
            </p>
          </div>
        </section>
        {/* Optional subtle SVG illustration for logistics/distribution */}
        <svg
          className="absolute bottom-0 right-0 w-96 h-48 opacity-10 pointer-events-none hidden md:block"
          viewBox="0 0 400 200"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <ellipse cx="300" cy="180" rx="100" ry="20" fill="#244296" />
          <rect x="220" y="120" width="60" height="30" rx="8" fill="#1d813f" />
          <rect x="320" y="140" width="40" height="20" rx="6" fill="#8133b5" />
          <rect x="260" y="160" width="30" height="10" rx="4" fill="#fcb900" />
        </svg>
        <section className="max-w-7xl mx-auto px-6 sm:px-8 py-16 sm:py-20 lg:py-24">
          {/* Service Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-20">
            {services.map((service, i) => (
              <motion.div
                key={service.title}
                initial={{ opacity: 0, y: 40 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true, amount: 0.3 }}
                transition={{ duration: 0.7, delay: i * 0.15 }}
                className="relative group bg-white rounded-2xl shadow-lg hover:shadow-2xl border border-neutral-100 hover:border-yellow-300 transition-all duration-300 flex flex-col p-8"
                style={{ boxShadow: '0 4px 24px 0 rgba(36,66,150,0.06)' }}
              >
                {/* Icon with brand color background */}
                <div className="w-14 h-14 flex items-center justify-center rounded-full mb-4 shadow-md" style={{ background: service.color }}>
                  <service.icon className="w-8 h-8 text-white" />
                </div>
                <h2 className="text-xl font-bold mb-2" style={{ color: service.color }}>{service.title}</h2>
                <div className="font-semibold text-neutral-900 mb-2">{service.summary}</div>
                <p className="text-neutral-600 mb-4 flex-1">{service.details}</p>
                {/* Innovative Feature Row */}
                <div className="flex gap-4 mb-6 justify-center">
                  {serviceFeatures[i].map((feature, idx) => (
                    <div key={idx} className="flex items-center gap-2 bg-neutral-100 rounded-full px-3 py-1 text-xs font-semibold text-primary-900 shadow-sm">
                      <feature.icon className="w-4 h-4 text-accent-500" />
                      <span>{feature.label}</span>
                    </div>
                  ))}
                </div>
                <a
                  href={`mailto:${service.contact}`}
                  className="inline-flex items-center gap-2 bg-[#fcb900] text-white font-semibold rounded-full px-5 py-2 shadow hover:shadow-lg hover:bg-yellow-400 transition-all focus:outline-none focus:ring-2 focus:ring-yellow-300"
                  style={{ boxShadow: '0 2px 8px 0 rgba(252,185,0,0.12)' }}
                >
                  Contact Team <ArrowRight className="w-4 h-4" />
                </a>
              </motion.div>
            ))}
          </div>

          {/* Service Process Timeline */}
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.3 }}
            variants={{
              hidden: {},
              visible: { transition: { staggerChildren: 0.18 } },
            }}
            className="bg-neutral-100 rounded-2xl py-12 px-4 sm:px-8 shadow-lg flex flex-col items-center"
          >
            <h2 className="text-xl sm:text-2xl font-bold text-primary-900 mb-10 text-center">Our Service Process</h2>
            <div className="w-full flex flex-col md:flex-row justify-between items-center gap-8 md:gap-0">
              {processSteps.map((step, i) => (
                <motion.div
                  key={step.number}
                  variants={{
                    hidden: { opacity: 0, y: 40 },
                    visible: { opacity: 1, y: 0 },
                  }}
                  transition={{ duration: 0.7, delay: i * 0.1 }}
                  className="flex flex-col items-center text-center flex-1 px-2 mb-10 md:mb-0"
                >
                  <div
                    className="w-16 h-16 flex items-center justify-center rounded-full mb-4 shadow-lg text-2xl font-bold"
                    style={{ background: step.color, color: step.number === '04' ? '#244296' : '#fff', boxShadow: '0 2px 12px 0 rgba(36,66,150,0.10)' }}
                  >
                    {step.number}
                  </div>
                  <div className="bg-white rounded-xl shadow p-4 mb-2 w-full max-w-xs mx-auto">
                    <div className="text-lg font-semibold mb-1" style={{ color: step.color }}>{step.title}</div>
                    <div className="text-neutral-700 text-sm">{step.desc}</div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </section>
      </div>
    </>
  );
};

export default Services;