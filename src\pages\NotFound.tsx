import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { Home, ArrowLeft, Search } from 'lucide-react';
import SEO from '../components/common/SEO';

const NotFound: React.FC = () => {
  return (
    <>
      <SEO
        title="Page Not Found - EverydayValueJM"
        description="The page you're looking for doesn't exist. Return to EverydayValueJM homepage or explore our products and services."
        keywords="404, page not found, EverydayValueJM"
      />

      <div className="pt-20 min-h-screen bg-gradient-to-br from-neutral-50 to-white flex items-center justify-center">
        <div className="container mx-auto px-6 sm:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-2xl mx-auto"
          >
            {/* 404 Animation */}
            <motion.div
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="mb-8"
            >
              <div className="text-8xl sm:text-9xl font-bold text-accent-500 mb-4">404</div>
              <div className="w-24 h-1 bg-accent-500 mx-auto rounded-full"></div>
            </motion.div>

            {/* Content */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="mb-12"
            >
              <h1 className="text-3xl sm:text-4xl font-heading font-bold text-primary-900 mb-6">
                Oops! Page Not Found
              </h1>
              <p className="text-lg text-neutral-600 leading-relaxed mb-8">
                The page you're looking for seems to have wandered off. Don't worry though – 
                our distribution network is much more reliable than our web navigation!
              </p>
              <p className="text-neutral-500">
                Let's get you back on track to discover Jamaica's premier distribution services.
              </p>
            </motion.div>

            {/* Action Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="flex flex-col sm:flex-row gap-4 justify-center mb-12"
            >
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Link
                  to="/"
                  className="bg-accent-500 hover:bg-accent-600 text-white px-8 py-4 rounded-full font-semibold text-lg transition-all duration-300 flex items-center space-x-2"
                >
                  <Home className="w-5 h-5" />
                  <span>Go Home</span>
                </Link>
              </motion.div>

              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <button
                  onClick={() => window.history.back()}
                  className="border-2 border-accent-500 text-accent-500 hover:bg-accent-500 hover:text-white px-8 py-4 rounded-full font-semibold text-lg transition-all duration-300 flex items-center space-x-2"
                >
                  <ArrowLeft className="w-5 h-5" />
                  <span>Go Back</span>
                </button>
              </motion.div>
            </motion.div>

            {/* Quick Links */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
              className="bg-white rounded-2xl shadow-lg p-8 border border-neutral-100"
            >
              <h2 className="text-xl font-heading font-bold text-primary-900 mb-6">
                Popular Pages
              </h2>
              <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-4">
                {[
                  { path: '/brands', label: 'Our Brands', icon: '🏷️' },
                  { path: '/products', label: 'Products', icon: '📦' },
                  { path: '/services', label: 'Services', icon: '🚚' },
                  { path: '/contact', label: 'Contact Us', icon: '📞' }
                ].map((link, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.4, delay: 1 + index * 0.1 }}
                  >
                    <Link
                      to={link.path}
                      className="block bg-neutral-50 hover:bg-accent-50 p-4 rounded-xl transition-all duration-300 group"
                    >
                      <div className="text-2xl mb-2">{link.icon}</div>
                      <div className="text-primary-900 font-semibold group-hover:text-accent-600 transition-colors">
                        {link.label}
                      </div>
                    </Link>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Search Suggestion */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 1.2 }}
              className="mt-8 text-neutral-500 text-sm"
            >
              <Search className="w-4 h-4 inline mr-2" />
              Looking for something specific? Try our search or contact our team for assistance.
            </motion.div>
          </motion.div>
        </div>
      </div>
    </>
  );
};

export default NotFound;