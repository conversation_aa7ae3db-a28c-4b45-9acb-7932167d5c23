import React from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { BarChart3, Truck, Warehouse, ArrowRight, Mail } from 'lucide-react';

const Services: React.FC = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const services = [
    {
      icon: BarChart3,
      title: 'Marketing and Sales',
      description: 'We help our suppliers to set and achieve their sales target through the provision of customized Advertising and Marketing services.',
      details: 'We know how challenging it can be to achieve and to maintain a strong presence in a robust and competitive market such as the Retail. This is why, Everyday Value Jamaica has taken this integrated approach which serves not only our suppliers – but also our retailers, island-wide.',
      contact: '<EMAIL>',
      color: 'from-blue-500 to-blue-600',
      hoverColor: 'group-hover:from-blue-600 group-hover:to-blue-700'
    },
    {
      icon: Truck,
      title: 'Delivery and Merchandising',
      description: 'In almost every town and nearby community across Jamaica, we deliver high quality consumer brands within multiple categories.',
      details: 'Foods, Beverages, Confectioneries, Personal Care, Household Supplies, Car Accessories and Pet Supplies. Our highly trained and proficient merchandisers maintain the highest level of standard when placing goods and replenishing stocks. We take pride in product presentation, and that\'s why we want "U" to be seen as "crem dela crem".',
      contact: '<EMAIL>',
      color: 'from-green-500 to-green-600',
      hoverColor: 'group-hover:from-green-600 group-hover:to-green-700'
    },
    {
      icon: Warehouse,
      title: 'Warehousing and Customer Care',
      description: 'For best practices in warehouse picking, organizing, and inventory management, Everyday Value Jamaica constantly monitors our systems.',
      details: 'We achieve the most reliable and up-to-date information necessary to meet and exceed customer expectations. Our in-house customer support makes it easy for our partnering stores to query and make adjustments to orders placed through our UOrder platform, as well as those orders sent directly to our email.',
      contact: '<EMAIL>',
      color: 'from-purple-500 to-purple-600',
      hoverColor: 'group-hover:from-purple-600 group-hover:to-purple-700'
    }
  ];

  return (
    <section id="services" className="py-16 sm:py-20 lg:py-24 bg-white relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute top-0 right-0 w-1/3 h-full bg-gradient-to-l from-neutral-50 to-transparent"></div>
      
      <div className="container mx-auto px-6 sm:px-8 relative z-10">
        <motion.div
          ref={ref}
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-16 sm:mb-20"
        >
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-heading font-bold text-primary-900 mb-6 sm:mb-8">
            Our Services
          </h2>
          <p className="text-lg sm:text-xl text-neutral-600 max-w-4xl mx-auto leading-relaxed px-4">
            Comprehensive distribution solutions designed to maximize your brand's reach 
            and impact across Jamaica's diverse markets.
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 sm:gap-10 mb-16 sm:mb-20">
          {services.map((service, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 50 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              className="group relative"
            >
              {/* Main Card */}
              <div className="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border border-neutral-100 group-hover:border-neutral-200 h-full flex flex-col">
                {/* Header */}
                <div className={`bg-gradient-to-r ${service.color} ${service.hoverColor} p-6 sm:p-8 text-white transition-all duration-500`}>
                  <service.icon className="w-10 h-10 sm:w-12 sm:h-12 mb-4 group-hover:scale-110 transition-transform duration-300" />
                  <h3 className="text-xl sm:text-2xl font-heading font-bold mb-3">
                    {service.title}
                  </h3>
                  <p className="text-white/90 leading-relaxed text-sm sm:text-base">
                    {service.description}
                  </p>
                </div>

                {/* Content */}
                <div className="p-6 sm:p-8 flex-1 flex flex-col">
                  <p className="text-neutral-600 text-sm sm:text-base leading-relaxed mb-6 flex-1">
                    {service.details}
                  </p>

                  {/* Contact Info */}
                  <div className="mb-6">
                    <div className="flex items-center space-x-2 text-accent-600 text-sm font-medium">
                      <Mail className="w-4 h-4" />
                      <a 
                        href={`mailto:${service.contact}`}
                        className="hover:text-accent-700 transition-colors"
                      >
                        {service.contact}
                      </a>
                    </div>
                  </div>

                  {/* CTA */}
                  <motion.button
                    className="group/btn flex items-center space-x-2 text-accent-500 hover:text-accent-600 font-semibold transition-colors text-sm sm:text-base"
                    whileHover={{ x: 5 }}
                  >
                    <span>Learn More</span>
                    <ArrowRight className="w-4 h-4 group-hover/btn:translate-x-1 transition-transform" />
                  </motion.button>
                </div>

                {/* Hover Effect Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-accent-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
              </div>

              {/* Floating Info Panel */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8, y: 20 }}
                animate={inView ? { opacity: 1, scale: 1, y: 0 } : {}}
                transition={{ duration: 0.6, delay: 0.6 + index * 0.2 }}
                className="absolute -bottom-4 -right-4 bg-accent-500 text-white p-3 sm:p-4 rounded-xl shadow-lg opacity-0 group-hover:opacity-100 transition-all duration-300 transform group-hover:scale-105 hidden sm:block"
              >
                <div className="text-sm font-semibold">Available 24/7</div>
                <div className="text-xs opacity-90">Contact our team</div>
              </motion.div>
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="text-center"
        >
          <div className="bg-gradient-to-r from-primary-900 to-primary-700 rounded-2xl p-8 sm:p-12 text-white">
            <h3 className="text-2xl sm:text-3xl font-heading font-bold mb-4 sm:mb-6">
              Ready to Partner with Us?
            </h3>
            <p className="text-lg sm:text-xl text-neutral-200 mb-8 sm:mb-10 max-w-3xl mx-auto leading-relaxed">
              Let's discuss how our comprehensive services can help your brand reach new heights in Jamaica.
            </p>
            <motion.button
              className="bg-accent-500 hover:bg-accent-600 text-white px-6 sm:px-8 py-3 sm:py-4 rounded-full font-semibold text-base sm:text-lg transition-all duration-300 inline-flex items-center space-x-2"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <span>Get Started Today</span>
              <ArrowRight className="w-5 h-5" />
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Services;