import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link, useLocation } from 'react-router-dom';
import { Menu, X, Phone, Mail, MapPin, ChevronDown, Factory, Truck } from 'lucide-react';

const Header: React.FC = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isWhatWeDoOpen, setIsWhatWeDoOpen] = useState(false);
  const location = useLocation();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMobileMenuOpen(false);
    setIsWhatWeDoOpen(false);
    window.scrollTo({ top: 0, left: 0, behavior: 'instant' });
  }, [location]);

  const navItems = [
    { path: '/', label: 'Home' },
    { path: '/who-we-are', label: 'Who We Are' },
    {
      path: '/what-we-do',
      label: 'What We Do',
      hasDropdown: true,
      dropdownItems: [
        { path: '/distribution', label: 'Distribution', icon: Truck },
        { path: '/manufacturing', label: 'Manufacturing', icon: Factory }
      ]
    },
    { path: '/brands', label: 'Brands' },
    { path: '/careers', label: 'Careers' },
    { path: '/contact', label: 'Contact' }
  ];

  const isActive = (path: string) => {
    if (path === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(path);
  };

  const isWhatWeDoActive = () => {
    return location.pathname === '/what-we-do' ||
           location.pathname === '/manufacturing' ||
           location.pathname === '/distribution';
  };

  return (
    <>
      {/* Top Contact Bar */}
      <div className={`fixed top-0 left-0 right-0 z-40 bg-accent-500 text-white py-3 text-sm transition-all duration-300 ${
        isScrolled ? 'opacity-0 -translate-y-full' : 'opacity-100 translate-y-0'
      }`}>
        <div className="container mx-auto px-6 sm:px-8">
          <div className="flex items-center justify-between">
            <div className="hidden md:flex items-center space-x-4">
              <span>Follow us on:</span>
              <div className="flex items-center space-x-3">
                <a href="#" className="hover:text-accent-200 transition-colors">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                  </svg>
                </a>
                <a href="#" className="hover:text-accent-200 transition-colors">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                  </svg>
                </a>
                <a href="#" className="hover:text-accent-200 transition-colors">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                  </svg>
                </a>
              </div>
            </div>
            <div className="flex items-center space-x-8">
              <div className="flex items-center space-x-2">
                <MapPin size={16} />
                <span>12 Colbeck Avenue, Kingston 10</span>
              </div>
              <div className="flex items-center space-x-2">
                <Phone size={16} />
                <span>(*************</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <motion.header
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        className={`fixed ${isScrolled ? 'top-0' : 'top-11'} left-0 right-0 z-50 transition-all duration-300 ${
          isScrolled
            ? 'bg-white shadow-lg'
            : 'bg-white shadow-sm'
        }`}
      >
        <div className="container mx-auto px-6 sm:px-8">
          <div className="flex items-center justify-between h-20"> {/* Optimized height for better fit */}
            {/* Logo */}
            <motion.div
              className="flex items-center space-x-4"
              whileHover={{ scale: 1.05 }}
            >
              <Link to="/" className="flex items-center space-x-4">
                <img
                  src="/images/everyday_logo_sm.png"
                  alt="Everyday Value Jamaica"
                  className="h-14 w-auto" // Slightly bigger logo
                />
                <div className="hidden sm:block">
                  <h1 className="text-xl font-heading font-bold text-primary-900">
                    Everyday Value Jamaica
                  </h1>
                  <p className="text-sm text-neutral-600">
                    Best Brands. Best Price. Best Choice.
                  </p>
                </div>
              </Link>
            </motion.div>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-6 xl:space-x-8">
              {navItems.map((item) => (
                <motion.div key={item.path} className="relative">
                  {item.hasDropdown ? (
                    <div
                      className="relative"
                      onMouseEnter={() => setIsWhatWeDoOpen(true)}
                      onMouseLeave={() => setIsWhatWeDoOpen(false)}
                    >
                      <Link
                        to={item.path}
                        className={`text-base font-semibold transition-colors duration-200 relative py-3 px-1 whitespace-nowrap flex items-center space-x-1 ${
                          isWhatWeDoActive()
                            ? 'text-accent-500'
                            : 'text-neutral-700 hover:text-accent-500'
                        }`}
                      >
                        <span>{item.label}</span>
                        <ChevronDown className={`w-4 h-4 transition-transform duration-200 ${
                          isWhatWeDoOpen ? 'rotate-180' : ''
                        }`} />
                        {isWhatWeDoActive() && (
                          <motion.div
                            layoutId="activeTab"
                            className="absolute -bottom-1 left-0 right-0 h-1 bg-accent-500 rounded-full"
                            initial={false}
                            transition={{ type: "spring", stiffness: 500, damping: 30 }}
                          />
                        )}
                      </Link>

                      {/* Dropdown Menu */}
                      <AnimatePresence>
                        {isWhatWeDoOpen && (
                          <motion.div
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: 10 }}
                            transition={{ duration: 0.2 }}
                            className="absolute top-full left-0 mt-2 w-64 bg-white rounded-xl shadow-xl border border-neutral-200 py-2 z-50"
                          >
                            {item.dropdownItems?.map((dropdownItem) => (
                              <Link
                                key={dropdownItem.path}
                                to={dropdownItem.path}
                                className="flex items-center space-x-3 px-4 py-3 text-neutral-700 hover:bg-neutral-50 hover:text-accent-500 transition-colors"
                              >
                                <dropdownItem.icon className="w-5 h-5 text-accent-500" />
                                <span className="font-medium">{dropdownItem.label}</span>
                              </Link>
                            ))}
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>
                  ) : (
                    <Link
                      to={item.path}
                      className={`text-base font-semibold transition-colors duration-200 relative py-3 px-1 whitespace-nowrap ${
                        isActive(item.path)
                          ? 'text-accent-500'
                          : 'text-neutral-700 hover:text-accent-500'
                      }`}
                    >
                      {item.label}
                      {isActive(item.path) && (
                        <motion.div
                          layoutId="activeTab"
                          className="absolute -bottom-1 left-0 right-0 h-1 bg-accent-500 rounded-full"
                          initial={false}
                          transition={{ type: "spring", stiffness: 500, damping: 30 }}
                        />
                      )}
                    </Link>
                  )}
                </motion.div>
              ))}
            </nav>

            {/* Contact Button & Mobile Menu */}
            <div className="flex items-center space-x-4">
              <div className="hidden lg:block">
                <Link
                  to="/contact"
                  className="bg-accent-500 hover:bg-accent-600 text-white px-6 py-3 rounded-full font-semibold text-sm whitespace-nowrap transition-all duration-300 shadow-lg hover:shadow-xl"
                >
                  Get In Touch
                </Link>
              </div>

              {/* Mobile Menu Button */}
              <button
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="lg:hidden p-3 rounded-md text-neutral-700"
              >
                {isMobileMenuOpen ? <X size={28} /> : <Menu size={28} />}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Menu */}
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{
            opacity: isMobileMenuOpen ? 1 : 0,
            height: isMobileMenuOpen ? 'auto' : 0
          }}
          className="lg:hidden bg-white border-t border-neutral-200 overflow-hidden"
        >
          <div className="container mx-auto px-6 sm:px-8 py-8">
            <nav className="flex flex-col space-y-6">
              {navItems.map((item) => (
                <Link
                  key={item.path}
                  to={item.path}
                  className={`text-left text-lg font-semibold transition-colors py-3 ${
                    isActive(item.path)
                      ? 'text-accent-500 font-bold'
                      : 'text-neutral-700 hover:text-accent-500'
                  }`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  {item.label}
                </Link>
              ))}
            </nav>

            {/* Mobile Contact Button */}
            <div className="mt-8 pt-8 border-t border-neutral-200">
              <Link
                to="/contact"
                className="block w-full bg-accent-500 hover:bg-accent-600 text-white text-center px-8 py-4 rounded-full font-semibold text-lg transition-all duration-300 mb-6"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Get In Touch
              </Link>

              <div className="space-y-4">
                <a
                  href="tel:+1************"
                  className="flex items-center space-x-3 text-neutral-700 text-base"
                >
                  <Phone size={20} />
                  <span>************</span>
                </a>
                <a
                  href="mailto:<EMAIL>"
                  className="flex items-center space-x-3 text-neutral-700 text-base"
                >
                  <Mail size={20} />
                  <span><EMAIL></span>
                </a>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.header>


    </>
  );
};

export default Header;